cmake_minimum_required(VERSION 3.20)

# Project Playtime Engine - Next-Generation Game Engine
project(ProjectPlaytimeEngine
    VERSION 1.0.0
    DESCRIPTION "Next-generation high-performance game engine supporting 2D, 2.5D, and 3D development"
    LANGUAGES CXX
)

# Set C++20 standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Platform detection
if(WIN32)
    set(PLATFORM_WINDOWS TRUE)
    add_definitions(-DOS_WINDOWS)
    message(STATUS "Building for Windows")
elseif(UNIX AND NOT APPLE)
    set(PLATFORM_LINUX TRUE)
    add_definitions(-DOS_LINUX)
    message(STATUS "Building for Linux")
else()
    message(FATAL_ERROR "Unsupported platform")
endif()

# Build configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Build type" FORCE)
endif()

message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")

# Compiler-specific settings
if(MSVC)
    # MSVC specific settings
    add_compile_options(/W4 /WX /permissive-)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    add_definitions(-DNOMINMAX)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
elseif(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    # GCC/Clang specific settings
    add_compile_options(-Wall -Wextra -Werror -pedantic)
    # Apply C++ specific flags only to C++ files
    add_compile_options($<$<COMPILE_LANGUAGE:CXX>:-fno-exceptions>)
    add_compile_options($<$<COMPILE_LANGUAGE:CXX>:-fno-rtti>)
endif()

# Debug/Release specific settings
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG -D_DEBUG)
    if(MSVC)
        add_compile_options(/Od /Zi)
    else()
        add_compile_options(-O0 -g)
    endif()
else()
    add_definitions(-DNDEBUG)
    if(MSVC)
        add_compile_options(/O2)
    else()
        add_compile_options(-O3 -DNDEBUG)
    endif()
endif()

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/src)

# Use pkg-config to find dependencies
find_package(PkgConfig REQUIRED)

# Platform-specific SDK configuration
if(PLATFORM_WINDOWS)
    # DirectX 12 Agility SDK
    find_path(DX12_INCLUDE_DIR d3d12.h
        HINTS
        "$ENV{DXSDK_DIR}/Include"
        "C:/Program Files (x86)/Windows Kits/10/Include/*/um"
        "C:/Program Files/Microsoft DirectX SDK*/Include"
    )

    if(DX12_INCLUDE_DIR)
        message(STATUS "Found DirectX 12 SDK: ${DX12_INCLUDE_DIR}")
        include_directories(${DX12_INCLUDE_DIR})
    else()
        message(WARNING "DirectX 12 SDK not found")
    endif()

elseif(PLATFORM_LINUX)
    # Vulkan SDK
    find_package(Vulkan REQUIRED)
    if(Vulkan_FOUND)
        message(STATUS "Found Vulkan SDK: ${Vulkan_INCLUDE_DIRS}")
        include_directories(${Vulkan_INCLUDE_DIRS})
    else()
        message(FATAL_ERROR "Vulkan SDK not found")
    endif()
endif()

# External dependencies using pkg-config when possible, fallback to find_package
# SDL2 for cross-platform windowing and input
pkg_check_modules(SDL2 QUIET sdl2)
if(NOT SDL2_FOUND)
    find_package(SDL2 QUIET)
    if(NOT SDL2_FOUND)
        message(STATUS "SDL2 not found via pkg-config or find_package, using system headers")
        # Try to find SDL2 headers in common locations
        find_path(SDL2_INCLUDE_DIRS SDL.h
            HINTS
            /usr/include/SDL2
            /usr/local/include/SDL2
            /opt/homebrew/include/SDL2
        )
        find_library(SDL2_LIBRARIES SDL2
            HINTS
            /usr/lib
            /usr/local/lib
            /opt/homebrew/lib
        )
        if(SDL2_INCLUDE_DIRS AND SDL2_LIBRARIES)
            set(SDL2_FOUND TRUE)
            message(STATUS "Found SDL2: ${SDL2_INCLUDE_DIRS}")
        endif()
    endif()
endif()

# EnTT for ECS framework (header-only, try to find system install)
find_path(ENTT_INCLUDE_DIR entt/entt.hpp
    HINTS
    /usr/include
    /usr/local/include
    /opt/homebrew/include
)

if(ENTT_INCLUDE_DIR)
    message(STATUS "Found EnTT: ${ENTT_INCLUDE_DIR}")
    include_directories(${ENTT_INCLUDE_DIR})
    set(ENTT_FOUND TRUE)
else()
    message(STATUS "EnTT not found in system, will use placeholder")
    set(ENTT_FOUND FALSE)
endif()

# For now, we'll build without external dependencies if they're not available
# This allows us to test our core engine systems
if(SDL2_FOUND)
    include_directories(${SDL2_INCLUDE_DIRS})
    link_directories(${SDL2_LIBRARY_DIRS})
    add_definitions(-DHAS_SDL2)
    message(STATUS "SDL2 support enabled")
else()
    message(STATUS "SDL2 not available - building with platform-specific windowing")
endif()

if(ENTT_FOUND)
    add_definitions(-DHAS_ENTT)
    message(STATUS "EnTT support enabled")
else()
    message(STATUS "EnTT not available - using basic entity system")
endif()

# Global compile definitions
add_definitions(-DENGINE_VERSION_MAJOR=1)
add_definitions(-DENGINE_VERSION_MINOR=0)
add_definitions(-DENGINE_VERSION_PATCH=0)

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Add subdirectories
add_subdirectory(src)

# Testing (only add if we want to include tests)
option(BUILD_TESTING "Build tests" OFF)
if(BUILD_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()

# Documentation
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    add_custom_target(docs
        ${DOXYGEN_EXECUTABLE}
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM
    )
endif()

# Installation
install(TARGETS ProjectPlaytimeEngine
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY include/ DESTINATION include)

# Package configuration
set(CPACK_PACKAGE_NAME "ProjectPlaytimeEngine")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "${PROJECT_DESCRIPTION}")
include(CPack)