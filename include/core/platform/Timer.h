#pragma once

/**
 * @file Timer.h
 * @brief High-resolution timing utilities
 */

#include "../Types.h"
#include <chrono>

namespace PlaytimeEngine {

/**
 * @brief High-resolution timer class
 */
class ENGINE_API Timer {
public:
    /**
     * @brief Constructor
     */
    Timer();
    
    /**
     * @brief Start/restart the timer
     */
    void start();
    
    /**
     * @brief Stop the timer
     */
    void stop();
    
    /**
     * @brief Reset the timer
     */
    void reset();
    
    /**
     * @brief Get elapsed time in seconds
     * @return Elapsed time in seconds
     */
    double getElapsedSeconds() const;
    
    /**
     * @brief Get elapsed time in milliseconds
     * @return Elapsed time in milliseconds
     */
    double getElapsedMilliseconds() const;
    
    /**
     * @brief Get elapsed time in microseconds
     * @return Elapsed time in microseconds
     */
    uint64 getElapsedMicroseconds() const;
    
    /**
     * @brief Get elapsed time in nanoseconds
     * @return Elapsed time in nanoseconds
     */
    uint64 getElapsedNanoseconds() const;
    
    /**
     * @brief Check if timer is running
     * @return True if running
     */
    bool isRunning() const { return running_; }

private:
    std::chrono::high_resolution_clock::time_point startTime_;
    std::chrono::high_resolution_clock::time_point endTime_;
    bool running_;
};

/**
 * @brief Scoped timer for automatic timing
 */
class ENGINE_API ScopedTimer {
public:
    /**
     * @brief Constructor
     * @param name Timer name for logging
     */
    explicit ScopedTimer(const String& name);
    
    /**
     * @brief Destructor logs elapsed time
     */
    ~ScopedTimer();

private:
    String name_;
    Timer timer_;
};

/**
 * @brief Frame rate counter
 */
class ENGINE_API FrameRateCounter {
public:
    /**
     * @brief Constructor
     * @param sampleCount Number of frames to average over
     */
    explicit FrameRateCounter(uint32 sampleCount = 60);
    
    /**
     * @brief Update with new frame
     */
    void update();
    
    /**
     * @brief Get current FPS
     * @return Frames per second
     */
    float getFPS() const { return fps_; }
    
    /**
     * @brief Get average frame time in milliseconds
     * @return Average frame time
     */
    float getAverageFrameTime() const { return averageFrameTime_; }
    
    /**
     * @brief Get minimum frame time in milliseconds
     * @return Minimum frame time
     */
    float getMinFrameTime() const { return minFrameTime_; }
    
    /**
     * @brief Get maximum frame time in milliseconds
     * @return Maximum frame time
     */
    float getMaxFrameTime() const { return maxFrameTime_; }
    
    /**
     * @brief Reset statistics
     */
    void reset();

private:
    std::vector<float> frameTimes_;
    uint32 sampleCount_;
    uint32 currentIndex_;
    uint32 frameCount_;
    float fps_;
    float averageFrameTime_;
    float minFrameTime_;
    float maxFrameTime_;
    Timer timer_;
};

/**
 * @brief Global timing functions
 */
namespace Timing {
    /**
     * @brief Get current time in seconds since epoch
     * @return Time in seconds
     */
    ENGINE_API double getCurrentTimeSeconds();
    
    /**
     * @brief Get current time in milliseconds since epoch
     * @return Time in milliseconds
     */
    ENGINE_API uint64 getCurrentTimeMilliseconds();
    
    /**
     * @brief Get current time in microseconds since epoch
     * @return Time in microseconds
     */
    ENGINE_API uint64 getCurrentTimeMicroseconds();
    
    /**
     * @brief Get current time in nanoseconds since epoch
     * @return Time in nanoseconds
     */
    ENGINE_API uint64 getCurrentTimeNanoseconds();
    
    /**
     * @brief Get high-resolution timer frequency
     * @return Timer frequency in Hz
     */
    ENGINE_API uint64 getTimerFrequency();
    
    /**
     * @brief Convert timer ticks to seconds
     * @param ticks Timer ticks
     * @return Time in seconds
     */
    ENGINE_API double ticksToSeconds(uint64 ticks);
    
    /**
     * @brief Convert seconds to timer ticks
     * @param seconds Time in seconds
     * @return Timer ticks
     */
    ENGINE_API uint64 secondsToTicks(double seconds);
    
    /**
     * @brief Sleep for specified duration
     * @param milliseconds Sleep duration in milliseconds
     */
    ENGINE_API void sleep(uint32 milliseconds);
    
    /**
     * @brief High-precision sleep
     * @param microseconds Sleep duration in microseconds
     */
    ENGINE_API void sleepMicroseconds(uint64 microseconds);
}

} // namespace PlaytimeEngine

/**
 * @brief Scoped timer macro
 */
#define SCOPED_TIMER(name) PlaytimeEngine::ScopedTimer CONCAT(_timer, __LINE__)(name)
