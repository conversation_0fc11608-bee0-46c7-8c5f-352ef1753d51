#pragma once

/**
 * @file DynamicLibrary.h
 * @brief Cross-platform dynamic library loading
 */

#include "../Types.h"

namespace PlaytimeEngine {

/**
 * @brief Dynamic library handle
 */
class ENGINE_API DynamicLibrary : public NonCopyable {
public:
    /**
     * @brief Constructor
     */
    DynamicLibrary();
    
    /**
     * @brief Destructor
     */
    ~DynamicLibrary();
    
    /**
     * @brief Load library from file
     * @param filename Library filename
     * @return True if successful
     */
    bool load(const String& filename);
    
    /**
     * @brief Unload library
     */
    void unload();
    
    /**
     * @brief Check if library is loaded
     * @return True if loaded
     */
    bool isLoaded() const { return handle_ != nullptr; }
    
    /**
     * @brief Get function pointer
     * @param functionName Function name
     * @return Function pointer or nullptr if not found
     */
    void* getFunction(const String& functionName) const;
    
    /**
     * @brief Get typed function pointer
     * @tparam T Function type
     * @param functionName Function name
     * @return Typed function pointer or nullptr if not found
     */
    template<typename T>
    T getFunctionAs(const String& functionName) const {
        return reinterpret_cast<T>(getFunction(functionName));
    }
    
    /**
     * @brief Get library filename
     * @return Library filename
     */
    const String& getFilename() const { return filename_; }
    
    /**
     * @brief Get last error message
     * @return Error message
     */
    String getLastError() const;

private:
    void* handle_;
    String filename_;
};

/**
 * @brief Dynamic library utility functions
 */
namespace DynamicLibraryUtils {
    /**
     * @brief Get platform-specific library extension
     * @return Library extension (.dll on Windows, .so on Linux)
     */
    ENGINE_API String getLibraryExtension();
    
    /**
     * @brief Get platform-specific library prefix
     * @return Library prefix (empty on Windows, "lib" on Linux)
     */
    ENGINE_API String getLibraryPrefix();
    
    /**
     * @brief Build platform-specific library filename
     * @param baseName Base library name
     * @return Platform-specific filename
     */
    ENGINE_API String buildLibraryFilename(const String& baseName);
    
    /**
     * @brief Check if library exists
     * @param filename Library filename
     * @return True if library file exists
     */
    ENGINE_API bool libraryExists(const String& filename);
    
    /**
     * @brief Get library search paths
     * @return Vector of search paths
     */
    ENGINE_API std::vector<String> getLibrarySearchPaths();
    
    /**
     * @brief Find library in search paths
     * @param baseName Base library name
     * @return Full path to library or empty string if not found
     */
    ENGINE_API String findLibrary(const String& baseName);
}

} // namespace PlaytimeEngine
