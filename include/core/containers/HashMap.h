#pragma once

/**
 * @file HashMap.h
 * @brief Hash map container optimized for game engine use
 */

#include "../Types.h"
#include "../Hash.h"
#include <unordered_map>

namespace PlaytimeEngine {

/**
 * @brief Hash map container with custom allocator support
 */
template<typename Key, typename Value, typename Hash = Hasher<Key>>
class HashMap {
public:
    using key_type = Key;
    using mapped_type = Value;
    using value_type = std::pair<const Key, Value>;
    using size_type = size_t;
    using hasher = Hash;
    using key_equal = std::equal_to<Key>;
    using reference = value_type&;
    using const_reference = const value_type&;
    using pointer = value_type*;
    using const_pointer = const value_type*;
    
    using underlying_type = std::unordered_map<Key, Value, Hash>;
    using iterator = typename underlying_type::iterator;
    using const_iterator = typename underlying_type::const_iterator;

    /**
     * @brief Default constructor
     */
    HashMap() = default;
    
    /**
     * @brief Constructor with bucket count
     */
    explicit HashMap(size_type bucketCount) : map_(bucketCount) {}
    
    /**
     * @brief Constructor with initializer list
     */
    HashMap(std::initializer_list<value_type> init) : map_(init) {}
    
    // Element access
    mapped_type& operator[](const key_type& key) {
        return map_[key];
    }
    
    mapped_type& operator[](key_type&& key) {
        return map_[std::move(key)];
    }
    
    mapped_type& at(const key_type& key) {
        return map_.at(key);
    }
    
    const mapped_type& at(const key_type& key) const {
        return map_.at(key);
    }
    
    // Iterators
    iterator begin() { return map_.begin(); }
    const_iterator begin() const { return map_.begin(); }
    const_iterator cbegin() const { return map_.cbegin(); }
    
    iterator end() { return map_.end(); }
    const_iterator end() const { return map_.end(); }
    const_iterator cend() const { return map_.cend(); }
    
    // Capacity
    bool empty() const { return map_.empty(); }
    size_type size() const { return map_.size(); }
    size_type maxSize() const { return map_.max_size(); }
    
    // Modifiers
    void clear() { map_.clear(); }
    
    std::pair<iterator, bool> insert(const value_type& value) {
        return map_.insert(value);
    }
    
    std::pair<iterator, bool> insert(value_type&& value) {
        return map_.insert(std::move(value));
    }
    
    template<typename... Args>
    std::pair<iterator, bool> emplace(Args&&... args) {
        return map_.emplace(std::forward<Args>(args)...);
    }
    
    iterator erase(const_iterator pos) {
        return map_.erase(pos);
    }
    
    iterator erase(const_iterator first, const_iterator last) {
        return map_.erase(first, last);
    }
    
    size_type erase(const key_type& key) {
        return map_.erase(key);
    }
    
    void swap(HashMap& other) {
        map_.swap(other.map_);
    }
    
    // Lookup
    iterator find(const key_type& key) {
        return map_.find(key);
    }
    
    const_iterator find(const key_type& key) const {
        return map_.find(key);
    }
    
    size_type count(const key_type& key) const {
        return map_.count(key);
    }
    
    bool contains(const key_type& key) const {
        return map_.find(key) != map_.end();
    }
    
    std::pair<iterator, iterator> equalRange(const key_type& key) {
        return map_.equal_range(key);
    }
    
    std::pair<const_iterator, const_iterator> equalRange(const key_type& key) const {
        return map_.equal_range(key);
    }
    
    // Bucket interface
    size_type bucketCount() const { return map_.bucket_count(); }
    size_type maxBucketCount() const { return map_.max_bucket_count(); }
    size_type bucketSize(size_type n) const { return map_.bucket_size(n); }
    size_type bucket(const key_type& key) const { return map_.bucket(key); }
    
    // Hash policy
    float loadFactor() const { return map_.load_factor(); }
    float maxLoadFactor() const { return map_.max_load_factor(); }
    void maxLoadFactor(float ml) { map_.max_load_factor(ml); }
    
    void rehash(size_type count) { map_.rehash(count); }
    void reserve(size_type count) { map_.reserve(count); }
    
    // Hash function
    hasher hashFunction() const { return map_.hash_function(); }
    key_equal keyEq() const { return map_.key_eq(); }

private:
    underlying_type map_;
};

/**
 * @brief String hash map specialization
 */
template<typename Value>
using StringHashMap = HashMap<String, Value, Hasher<String>>;

/**
 * @brief Integer hash map specialization
 */
template<typename Value>
using IntHashMap = HashMap<int, Value, Hasher<int>>;

/**
 * @brief Hash set container
 */
template<typename Key, typename Hash = Hasher<Key>>
class HashSet {
public:
    using key_type = Key;
    using value_type = Key;
    using size_type = size_t;
    using hasher = Hash;
    using key_equal = std::equal_to<Key>;
    
    using underlying_type = std::unordered_set<Key, Hash>;
    using iterator = typename underlying_type::iterator;
    using const_iterator = typename underlying_type::const_iterator;

    // Constructors
    HashSet() = default;
    explicit HashSet(size_type bucketCount) : set_(bucketCount) {}
    HashSet(std::initializer_list<value_type> init) : set_(init) {}
    
    // Iterators
    iterator begin() { return set_.begin(); }
    const_iterator begin() const { return set_.begin(); }
    const_iterator cbegin() const { return set_.cbegin(); }
    
    iterator end() { return set_.end(); }
    const_iterator end() const { return set_.end(); }
    const_iterator cend() const { return set_.cend(); }
    
    // Capacity
    bool empty() const { return set_.empty(); }
    size_type size() const { return set_.size(); }
    size_type maxSize() const { return set_.max_size(); }
    
    // Modifiers
    void clear() { set_.clear(); }
    
    std::pair<iterator, bool> insert(const value_type& value) {
        return set_.insert(value);
    }
    
    std::pair<iterator, bool> insert(value_type&& value) {
        return set_.insert(std::move(value));
    }
    
    template<typename... Args>
    std::pair<iterator, bool> emplace(Args&&... args) {
        return set_.emplace(std::forward<Args>(args)...);
    }
    
    iterator erase(const_iterator pos) {
        return set_.erase(pos);
    }
    
    size_type erase(const key_type& key) {
        return set_.erase(key);
    }
    
    // Lookup
    iterator find(const key_type& key) {
        return set_.find(key);
    }
    
    const_iterator find(const key_type& key) const {
        return set_.find(key);
    }
    
    size_type count(const key_type& key) const {
        return set_.count(key);
    }
    
    bool contains(const key_type& key) const {
        return set_.find(key) != set_.end();
    }

private:
    underlying_type set_;
};

} // namespace PlaytimeEngine
