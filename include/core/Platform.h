#pragma once

/**
 * @file Platform.h
 * @brief Cross-platform abstraction layer for Project Playtime Engine
 * 
 * This header provides the main interface for platform-specific functionality,
 * abstracting differences between Windows and Linux operating systems.
 */

#include "Types.h"

namespace PlaytimeEngine {
namespace Platform {

/**
 * @brief Platform initialization result
 */
enum class InitResult {
    Success,
    Error,
    UnsupportedPlatform
};

/**
 * @brief Platform information structure
 */
struct PlatformInfo {
    String platformName;        ///< Platform name (e.g., "Windows", "Linux")
    String osVersion;          ///< Operating system version
    String cpuArchitecture;    ///< CPU architecture (e.g., "x64", "ARM64")
    uint32 cpuCoreCount;       ///< Number of CPU cores
    uint64 totalMemory;        ///< Total system memory in bytes
    uint64 availableMemory;    ///< Available system memory in bytes
    bool isDebuggerPresent;    ///< True if debugger is attached
};

/**
 * @brief Window handle type (platform-specific)
 */
#if PLATFORM_WINDOWS
    using WindowHandle = void*;  // HWND
#elif PLATFORM_LINUX
    using WindowHandle = unsigned long;  // Window (X11) or wl_surface* (Wayland)
#endif

/**
 * @brief Initialize the platform layer
 * @return Initialization result
 */
ENGINE_API InitResult initialize();

/**
 * @brief Shutdown the platform layer
 */
ENGINE_API void shutdown();

/**
 * @brief Get platform information
 * @return Platform information structure
 */
ENGINE_API PlatformInfo getPlatformInfo();

/**
 * @brief Get the current working directory
 * @return Current working directory path
 */
ENGINE_API String getCurrentDirectory();

/**
 * @brief Set the current working directory
 * @param path New working directory path
 * @return True if successful
 */
ENGINE_API bool setCurrentDirectory(const String& path);

/**
 * @brief Get the executable directory
 * @return Directory containing the executable
 */
ENGINE_API String getExecutableDirectory();

/**
 * @brief Get the user documents directory
 * @return User documents directory path
 */
ENGINE_API String getDocumentsDirectory();

/**
 * @brief Get the application data directory
 * @return Application data directory path
 */
ENGINE_API String getAppDataDirectory();

/**
 * @brief Get the temporary directory
 * @return Temporary directory path
 */
ENGINE_API String getTempDirectory();

/**
 * @brief Show a message box (platform-specific)
 * @param title Message box title
 * @param message Message content
 * @param isError True for error message, false for information
 */
ENGINE_API void showMessageBox(const String& title, const String& message, bool isError = false);

/**
 * @brief Check if a debugger is present
 * @return True if debugger is attached
 */
ENGINE_API bool isDebuggerPresent();

/**
 * @brief Trigger a debug break (if debugger is present)
 */
ENGINE_API void debugBreak();

/**
 * @brief Get the current thread ID
 * @return Current thread ID
 */
ENGINE_API uint32 getCurrentThreadId();

/**
 * @brief Set the current thread name
 * @param name Thread name
 */
ENGINE_API void setThreadName(const String& name);

/**
 * @brief Set thread affinity to specific CPU cores
 * @param threadId Thread ID (0 for current thread)
 * @param coreMask Bitmask of CPU cores
 * @return True if successful
 */
ENGINE_API bool setThreadAffinity(uint32 threadId, uint64 coreMask);

/**
 * @brief Get the number of CPU cores
 * @return Number of CPU cores
 */
ENGINE_API uint32 getCpuCoreCount();

/**
 * @brief Get the CPU cache line size
 * @return Cache line size in bytes
 */
ENGINE_API uint32 getCacheLineSize();

/**
 * @brief Sleep for the specified number of milliseconds
 * @param milliseconds Sleep duration
 */
ENGINE_API void sleep(uint32 milliseconds);

/**
 * @brief Yield the current thread's time slice
 */
ENGINE_API void yield();

/**
 * @brief Get high-resolution timestamp
 * @return Timestamp in nanoseconds
 */
ENGINE_API uint64 getHighResolutionTime();

/**
 * @brief Get the high-resolution timer frequency
 * @return Timer frequency in Hz
 */
ENGINE_API uint64 getHighResolutionFrequency();

/**
 * @brief Convert path separators to platform-specific format
 * @param path Input path
 * @return Path with platform-specific separators
 */
ENGINE_API String normalizePath(const String& path);

/**
 * @brief Check if a path is absolute
 * @param path Path to check
 * @return True if path is absolute
 */
ENGINE_API bool isAbsolutePath(const String& path);

/**
 * @brief Combine two paths
 * @param basePath Base path
 * @param relativePath Relative path to append
 * @return Combined path
 */
ENGINE_API String combinePaths(const String& basePath, const String& relativePath);

/**
 * @brief Get the parent directory of a path
 * @param path Input path
 * @return Parent directory path
 */
ENGINE_API String getParentDirectory(const String& path);

/**
 * @brief Get the filename from a path
 * @param path Input path
 * @return Filename
 */
ENGINE_API String getFileName(const String& path);

/**
 * @brief Get the file extension from a path
 * @param path Input path
 * @return File extension (including the dot)
 */
ENGINE_API String getFileExtension(const String& path);

/**
 * @brief Memory allocation with alignment
 * @param size Size in bytes
 * @param alignment Alignment in bytes (must be power of 2)
 * @return Aligned memory pointer, or nullptr on failure
 */
ENGINE_API void* alignedAlloc(size_t size, size_t alignment);

/**
 * @brief Free aligned memory
 * @param ptr Pointer returned by alignedAlloc
 */
ENGINE_API void alignedFree(void* ptr);

/**
 * @brief Virtual memory allocation
 * @param size Size in bytes
 * @param commit True to commit the memory immediately
 * @return Virtual memory pointer, or nullptr on failure
 */
ENGINE_API void* virtualAlloc(size_t size, bool commit = true);

/**
 * @brief Free virtual memory
 * @param ptr Pointer returned by virtualAlloc
 * @param size Size in bytes
 */
ENGINE_API void virtualFree(void* ptr, size_t size);

/**
 * @brief Commit virtual memory pages
 * @param ptr Virtual memory pointer
 * @param size Size in bytes
 * @return True if successful
 */
ENGINE_API bool virtualCommit(void* ptr, size_t size);

/**
 * @brief Decommit virtual memory pages
 * @param ptr Virtual memory pointer
 * @param size Size in bytes
 * @return True if successful
 */
ENGINE_API bool virtualDecommit(void* ptr, size_t size);

/**
 * @brief Get system page size
 * @return Page size in bytes
 */
ENGINE_API size_t getPageSize();

/**
 * @brief Get system allocation granularity
 * @return Allocation granularity in bytes
 */
ENGINE_API size_t getAllocationGranularity();

} // namespace Platform
} // namespace PlaytimeEngine
