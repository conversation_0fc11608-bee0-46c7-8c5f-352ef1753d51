#pragma once

/**
 * @file Types.h
 * @brief Core type definitions for Project Playtime Engine
 * 
 * Defines fundamental types, constants, and platform-specific configurations
 * used throughout the engine.
 */

#include <cstdint>
#include <cstddef>
#include <memory>
#include <string>

// Platform detection
#ifdef _WIN32
    #define PLATFORM_WINDOWS 1
    #define PLATFORM_LINUX 0
#elif defined(__linux__)
    #define PLATFORM_WINDOWS 0
    #define PLATFORM_LINUX 1
#else
    #error "Unsupported platform"
#endif

// Compiler detection
#ifdef _MSC_VER
    #define COMPILER_MSVC 1
    #define COMPILER_GCC 0
    #define COMPILER_CLANG 0
#elif defined(__GNUC__)
    #define COMPILER_MSVC 0
    #define COMPILER_GCC 1
    #define COMPILER_CLANG 0
#elif defined(__clang__)
    #define COMPILER_MSVC 0
    #define COMPILER_GCC 0
    #define COMPILER_CLANG 1
#endif

// Architecture detection
#ifdef _M_X64
    #define ARCH_X64 1
    #define ARCH_X86 0
#elif defined(_M_IX86)
    #define ARCH_X64 0
    #define ARCH_X86 1
#elif defined(__x86_64__)
    #define ARCH_X64 1
    #define ARCH_X86 0
#elif defined(__i386__)
    #define ARCH_X64 0
    #define ARCH_X86 1
#endif

// Build configuration
#ifdef _DEBUG
    #define BUILD_DEBUG 1
    #define BUILD_RELEASE 0
#else
    #define BUILD_DEBUG 0
    #define BUILD_RELEASE 1
#endif

namespace PlaytimeEngine {

// Fixed-width integer types
using int8 = std::int8_t;
using int16 = std::int16_t;
using int32 = std::int32_t;
using int64 = std::int64_t;

using uint8 = std::uint8_t;
using uint16 = std::uint16_t;
using uint32 = std::uint32_t;
using uint64 = std::uint64_t;

// Floating-point types
using float32 = float;
using float64 = double;

// Size types
using size_t = std::size_t;
using ptrdiff_t = std::ptrdiff_t;

// Character types
using char8 = char;
using char16 = char16_t;
using char32 = char32_t;
using wchar = wchar_t;

// String types
using String = std::string;
using WString = std::wstring;
using U16String = std::u16string;
using U32String = std::u32string;

// Smart pointer types
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// Engine-specific types
using EntityID = uint32;
using ComponentTypeID = uint32;
using SystemID = uint32;
using ResourceID = uint64;
using Handle = uint64;

// Time types
using TimeStamp = uint64;
using Duration = float64;

// Hash type
using Hash = uint64;

// Constants
constexpr EntityID INVALID_ENTITY_ID = 0;
constexpr ComponentTypeID INVALID_COMPONENT_TYPE_ID = 0;
constexpr SystemID INVALID_SYSTEM_ID = 0;
constexpr ResourceID INVALID_RESOURCE_ID = 0;
constexpr Handle INVALID_HANDLE = 0;

constexpr size_t KILOBYTE = 1024;
constexpr size_t MEGABYTE = 1024 * KILOBYTE;
constexpr size_t GIGABYTE = 1024 * MEGABYTE;

constexpr size_t CACHE_LINE_SIZE = 64;
constexpr size_t PAGE_SIZE = 4096;

// Alignment macros
#if COMPILER_MSVC
    #define ALIGN(x) __declspec(align(x))
    #define FORCE_INLINE __forceinline
    #define NO_INLINE __declspec(noinline)
    #define RESTRICT __restrict
#elif COMPILER_GCC || COMPILER_CLANG
    #define ALIGN(x) __attribute__((aligned(x)))
    #define FORCE_INLINE __attribute__((always_inline)) inline
    #define NO_INLINE __attribute__((noinline))
    #define RESTRICT __restrict__
#endif

// DLL export/import macros
#if PLATFORM_WINDOWS
    #ifdef ENGINE_EXPORTS
        #define ENGINE_API __declspec(dllexport)
    #else
        #define ENGINE_API __declspec(dllimport)
    #endif
#else
    #define ENGINE_API __attribute__((visibility("default")))
#endif

// Utility macros
#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))
#define OFFSET_OF(type, member) offsetof(type, member)
#define STRINGIFY(x) #x
#define CONCAT(a, b) a##b

// Bit manipulation macros
#define BIT(x) (1u << (x))
#define SET_BIT(value, bit) ((value) |= BIT(bit))
#define CLEAR_BIT(value, bit) ((value) &= ~BIT(bit))
#define TOGGLE_BIT(value, bit) ((value) ^= BIT(bit))
#define CHECK_BIT(value, bit) (((value) & BIT(bit)) != 0)

// Memory alignment utilities
template<typename T>
constexpr T alignUp(T value, size_t alignment) {
    return (value + alignment - 1) & ~(alignment - 1);
}

template<typename T>
constexpr T alignDown(T value, size_t alignment) {
    return value & ~(alignment - 1);
}

template<typename T>
constexpr bool isAligned(T value, size_t alignment) {
    return (value & (alignment - 1)) == 0;
}

// Safe casting utilities
template<typename To, typename From>
constexpr To safeCast(From value) {
    static_assert(sizeof(To) >= sizeof(From), "Unsafe cast: target type is smaller");
    return static_cast<To>(value);
}

// Enum class utilities
template<typename E>
constexpr auto toUnderlying(E e) noexcept {
    return static_cast<std::underlying_type_t<E>>(e);
}

// Non-copyable base class
class NonCopyable {
protected:
    NonCopyable() = default;
    ~NonCopyable() = default;
    
private:
    NonCopyable(const NonCopyable&) = delete;
    NonCopyable& operator=(const NonCopyable&) = delete;
};

// Non-movable base class
class NonMovable {
protected:
    NonMovable() = default;
    ~NonMovable() = default;
    
private:
    NonMovable(NonMovable&&) = delete;
    NonMovable& operator=(NonMovable&&) = delete;
};

// Result type for error handling
template<typename T, typename E = int>
class Result {
public:
    Result(const T& value) : value_(value), hasValue_(true) {}
    Result(const E& error) : error_(error), hasValue_(false) {}
    
    bool hasValue() const { return hasValue_; }
    bool hasError() const { return !hasValue_; }
    
    const T& value() const { 
        assert(hasValue_);
        return value_; 
    }
    
    const E& error() const { 
        assert(!hasValue_);
        return error_; 
    }
    
    operator bool() const { return hasValue_; }

private:
    union {
        T value_;
        E error_;
    };
    bool hasValue_;
};

// Optional type
template<typename T>
class Optional {
public:
    Optional() : hasValue_(false) {}
    Optional(const T& value) : value_(value), hasValue_(true) {}
    Optional(T&& value) : value_(std::move(value)), hasValue_(true) {}
    
    bool hasValue() const { return hasValue_; }
    operator bool() const { return hasValue_; }
    
    const T& value() const { 
        assert(hasValue_);
        return value_; 
    }
    
    T& value() { 
        assert(hasValue_);
        return value_; 
    }
    
    const T& valueOr(const T& defaultValue) const {
        return hasValue_ ? value_ : defaultValue;
    }
    
    void reset() { hasValue_ = false; }

private:
    T value_;
    bool hasValue_;
};

} // namespace PlaytimeEngine
