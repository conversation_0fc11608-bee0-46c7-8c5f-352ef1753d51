#pragma once

/**
 * @file WorkStealingDeque.h
 * @brief Lock-free work-stealing deque implementation
 */

#include "../Types.h"
#include <atomic>
#include <memory>

namespace PlaytimeEngine {

/**
 * @brief Lock-free work-stealing deque (Chase-Lev algorithm)
 * 
 * This deque allows the owner thread to push/pop from one end
 * while other threads can steal from the other end.
 */
template<typename T>
class WorkStealingDeque {
public:
    /**
     * @brief Constructor
     * @param capacity Initial capacity (must be power of 2)
     */
    explicit WorkStealingDeque(size_t capacity = 1024)
        : capacity_(capacity), mask_(capacity - 1) {
        // Ensure capacity is power of 2
        assert((capacity & (capacity - 1)) == 0);
        
        buffer_ = std::make_unique<std::atomic<T>[]>(capacity_);
        top_.store(0, std::memory_order_relaxed);
        bottom_.store(0, std::memory_order_relaxed);
    }
    
    /**
     * @brief Destructor
     */
    ~WorkStealingDeque() = default;
    
    /**
     * @brief Push item to the bottom (owner thread only)
     * @param item Item to push
     */
    void push(T item) {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_acquire);
        
        // Check if we need to resize
        if (b - t >= capacity_ - 1) {
            resize();
            t = top_.load(std::memory_order_acquire);
        }
        
        buffer_[b & mask_].store(item, std::memory_order_relaxed);
        std::atomic_thread_fence(std::memory_order_release);
        bottom_.store(b + 1, std::memory_order_relaxed);
    }
    
    /**
     * @brief Pop item from the bottom (owner thread only)
     * @return Item or nullptr if empty
     */
    T pop() {
        size_t b = bottom_.load(std::memory_order_relaxed) - 1;
        bottom_.store(b, std::memory_order_relaxed);
        std::atomic_thread_fence(std::memory_order_seq_cst);
        
        size_t t = top_.load(std::memory_order_relaxed);
        
        if (t <= b) {
            // Non-empty queue
            T item = buffer_[b & mask_].load(std::memory_order_relaxed);
            
            if (t == b) {
                // Single last element, compete with thieves
                if (!top_.compare_exchange_strong(t, t + 1, 
                                                 std::memory_order_seq_cst,
                                                 std::memory_order_relaxed)) {
                    // Lost race, queue is empty
                    item = T{};
                }
                bottom_.store(b + 1, std::memory_order_relaxed);
            }
            return item;
        } else {
            // Empty queue
            bottom_.store(b + 1, std::memory_order_relaxed);
            return T{};
        }
    }
    
    /**
     * @brief Steal item from the top (other threads)
     * @return Item or nullptr if empty/failed
     */
    T steal() {
        size_t t = top_.load(std::memory_order_acquire);
        std::atomic_thread_fence(std::memory_order_seq_cst);
        size_t b = bottom_.load(std::memory_order_acquire);
        
        if (t < b) {
            // Non-empty queue
            T item = buffer_[t & mask_].load(std::memory_order_relaxed);
            
            if (!top_.compare_exchange_strong(t, t + 1,
                                             std::memory_order_seq_cst,
                                             std::memory_order_relaxed)) {
                // Failed to steal
                return T{};
            }
            
            return item;
        }
        
        // Empty queue
        return T{};
    }
    
    /**
     * @brief Check if deque is empty
     * @return True if empty (approximate)
     */
    bool empty() const {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_relaxed);
        return b <= t;
    }
    
    /**
     * @brief Get approximate size
     * @return Approximate number of items
     */
    size_t size() const {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_relaxed);
        return b > t ? b - t : 0;
    }
    
    /**
     * @brief Get capacity
     * @return Current capacity
     */
    size_t capacity() const {
        return capacity_;
    }

private:
    /**
     * @brief Resize the deque (double capacity)
     */
    void resize() {
        size_t oldCapacity = capacity_;
        size_t newCapacity = oldCapacity * 2;
        size_t newMask = newCapacity - 1;
        
        auto newBuffer = std::make_unique<std::atomic<T>[]>(newCapacity);
        
        size_t t = top_.load(std::memory_order_relaxed);
        size_t b = bottom_.load(std::memory_order_relaxed);
        
        // Copy existing items
        for (size_t i = t; i < b; ++i) {
            newBuffer[i & newMask].store(
                buffer_[i & mask_].load(std::memory_order_relaxed),
                std::memory_order_relaxed
            );
        }
        
        buffer_ = std::move(newBuffer);
        capacity_ = newCapacity;
        mask_ = newMask;
    }

private:
    std::unique_ptr<std::atomic<T>[]> buffer_;
    size_t capacity_;
    size_t mask_;
    
    // Align to cache line to avoid false sharing
    alignas(CACHE_LINE_SIZE) std::atomic<size_t> top_;
    alignas(CACHE_LINE_SIZE) std::atomic<size_t> bottom_;
};

/**
 * @brief Specialized work-stealing deque for pointers
 */
template<typename T>
class WorkStealingDeque<T*> {
public:
    explicit WorkStealingDeque(size_t capacity = 1024)
        : capacity_(capacity), mask_(capacity - 1) {
        assert((capacity & (capacity - 1)) == 0);
        
        buffer_ = std::make_unique<std::atomic<T*>[]>(capacity_);
        for (size_t i = 0; i < capacity_; ++i) {
            buffer_[i].store(nullptr, std::memory_order_relaxed);
        }
        
        top_.store(0, std::memory_order_relaxed);
        bottom_.store(0, std::memory_order_relaxed);
    }
    
    void push(T* item) {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_acquire);
        
        if (b - t >= capacity_ - 1) {
            resize();
            t = top_.load(std::memory_order_acquire);
        }
        
        buffer_[b & mask_].store(item, std::memory_order_relaxed);
        std::atomic_thread_fence(std::memory_order_release);
        bottom_.store(b + 1, std::memory_order_relaxed);
    }
    
    T* pop() {
        size_t b = bottom_.load(std::memory_order_relaxed) - 1;
        bottom_.store(b, std::memory_order_relaxed);
        std::atomic_thread_fence(std::memory_order_seq_cst);
        
        size_t t = top_.load(std::memory_order_relaxed);
        
        if (t <= b) {
            T* item = buffer_[b & mask_].load(std::memory_order_relaxed);
            
            if (t == b) {
                if (!top_.compare_exchange_strong(t, t + 1,
                                                 std::memory_order_seq_cst,
                                                 std::memory_order_relaxed)) {
                    item = nullptr;
                }
                bottom_.store(b + 1, std::memory_order_relaxed);
            }
            return item;
        } else {
            bottom_.store(b + 1, std::memory_order_relaxed);
            return nullptr;
        }
    }
    
    T* steal() {
        size_t t = top_.load(std::memory_order_acquire);
        std::atomic_thread_fence(std::memory_order_seq_cst);
        size_t b = bottom_.load(std::memory_order_acquire);
        
        if (t < b) {
            T* item = buffer_[t & mask_].load(std::memory_order_relaxed);
            
            if (!top_.compare_exchange_strong(t, t + 1,
                                             std::memory_order_seq_cst,
                                             std::memory_order_relaxed)) {
                return nullptr;
            }
            
            return item;
        }
        
        return nullptr;
    }
    
    bool empty() const {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_relaxed);
        return b <= t;
    }
    
    size_t size() const {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_relaxed);
        return b > t ? b - t : 0;
    }
    
    size_t capacity() const {
        return capacity_;
    }

private:
    void resize() {
        size_t oldCapacity = capacity_;
        size_t newCapacity = oldCapacity * 2;
        size_t newMask = newCapacity - 1;
        
        auto newBuffer = std::make_unique<std::atomic<T*>[]>(newCapacity);
        for (size_t i = 0; i < newCapacity; ++i) {
            newBuffer[i].store(nullptr, std::memory_order_relaxed);
        }
        
        size_t t = top_.load(std::memory_order_relaxed);
        size_t b = bottom_.load(std::memory_order_relaxed);
        
        for (size_t i = t; i < b; ++i) {
            newBuffer[i & newMask].store(
                buffer_[i & mask_].load(std::memory_order_relaxed),
                std::memory_order_relaxed
            );
        }
        
        buffer_ = std::move(newBuffer);
        capacity_ = newCapacity;
        mask_ = newMask;
    }

private:
    std::unique_ptr<std::atomic<T*>[]> buffer_;
    size_t capacity_;
    size_t mask_;
    
    alignas(CACHE_LINE_SIZE) std::atomic<size_t> top_;
    alignas(CACHE_LINE_SIZE) std::atomic<size_t> bottom_;
};

} // namespace PlaytimeEngine
