#pragma once

/**
 * @file Engine.h
 * @brief Main engine class for Project Playtime Engine
 * 
 * The Engine class serves as the central coordinator for all engine subsystems,
 * managing initialization, shutdown, and the main game loop.
 */

#include "Types.h"
#include "SubsystemManager.h"
#include "GameLoop.h"
#include "EngineConfig.h"
#include <memory>

namespace PlaytimeEngine {

// Forward declarations
class IRenderer;
class JobSystem;
class MemoryManager;
class PluginManager;
class InputManager;
class AudioEngine;
class PhysicsWorld;
class SceneManager;

/**
 * @brief Main engine class that coordinates all subsystems
 * 
 * The Engine class is responsible for:
 * - Initializing and shutting down all engine subsystems
 * - Managing the main game loop
 * - Coordinating frame updates across subsystems
 * - Providing access to core engine services
 */
class ENGINE_API Engine : public NonCopyable {
public:
    /**
     * @brief Engine initialization result
     */
    enum class InitResult {
        Success,
        ConfigurationError,
        PlatformError,
        RendererError,
        SubsystemError,
        UnknownError
    };

    /**
     * @brief Engine state enumeration
     */
    enum class State {
        Uninitialized,
        Initializing,
        Running,
        Paused,
        Shutting_Down,
        Shutdown
    };

    /**
     * @brief Constructor
     */
    Engine();

    /**
     * @brief Destructor
     */
    ~Engine();

    /**
     * @brief Initialize the engine with the given configuration
     * @param config Engine configuration
     * @return Initialization result
     */
    InitResult initialize(const EngineConfig& config);

    /**
     * @brief Run the main engine loop
     * @return Exit code
     */
    int run();

    /**
     * @brief Shutdown the engine
     */
    void shutdown();

    /**
     * @brief Request engine shutdown
     */
    void requestShutdown();

    /**
     * @brief Pause the engine
     */
    void pause();

    /**
     * @brief Resume the engine from pause
     */
    void resume();

    /**
     * @brief Check if the engine is running
     * @return True if the engine is running
     */
    bool isRunning() const { return state_ == State::Running; }

    /**
     * @brief Check if the engine is paused
     * @return True if the engine is paused
     */
    bool isPaused() const { return state_ == State::Paused; }

    /**
     * @brief Get the current engine state
     * @return Current engine state
     */
    State getState() const { return state_; }

    /**
     * @brief Get the engine configuration
     * @return Reference to the engine configuration
     */
    const EngineConfig& getConfig() const { return config_; }

    /**
     * @brief Get the subsystem manager
     * @return Reference to the subsystem manager
     */
    SubsystemManager& getSubsystemManager() { return *subsystemManager_; }

    /**
     * @brief Get the game loop
     * @return Reference to the game loop
     */
    GameLoop& getGameLoop() { return *gameLoop_; }

    /**
     * @brief Get a subsystem by type
     * @tparam T Subsystem type
     * @return Pointer to the subsystem, or nullptr if not found
     */
    template<typename T>
    T* getSubsystem() {
        return subsystemManager_->getSubsystem<T>();
    }

    /**
     * @brief Get the renderer
     * @return Pointer to the renderer
     */
    IRenderer* getRenderer() const;

    /**
     * @brief Get the job system
     * @return Pointer to the job system
     */
    JobSystem* getJobSystem() const;

    /**
     * @brief Get the memory manager
     * @return Pointer to the memory manager
     */
    MemoryManager* getMemoryManager() const;

    /**
     * @brief Get the plugin manager
     * @return Pointer to the plugin manager
     */
    PluginManager* getPluginManager() const;

    /**
     * @brief Get the input manager
     * @return Pointer to the input manager
     */
    InputManager* getInputManager() const;

    /**
     * @brief Get the audio engine
     * @return Pointer to the audio engine
     */
    AudioEngine* getAudioEngine() const;

    /**
     * @brief Get the physics world
     * @return Pointer to the physics world
     */
    PhysicsWorld* getPhysicsWorld() const;

    /**
     * @brief Get the scene manager
     * @return Pointer to the scene manager
     */
    SceneManager* getSceneManager() const;

    /**
     * @brief Get the current frame number
     * @return Current frame number
     */
    uint64 getFrameNumber() const;

    /**
     * @brief Get the current frame time in seconds
     * @return Frame time in seconds
     */
    float getDeltaTime() const;

    /**
     * @brief Get the total elapsed time since engine start
     * @return Elapsed time in seconds
     */
    double getElapsedTime() const;

    /**
     * @brief Get the current frames per second
     * @return Current FPS
     */
    float getFPS() const;

    /**
     * @brief Get the singleton engine instance
     * @return Pointer to the engine instance
     */
    static Engine* getInstance() { return instance_; }

private:
    /**
     * @brief Initialize core subsystems
     * @return True if successful
     */
    bool initializeCore();

    /**
     * @brief Initialize platform-specific subsystems
     * @return True if successful
     */
    bool initializePlatform();

    /**
     * @brief Initialize rendering subsystem
     * @return True if successful
     */
    bool initializeRenderer();

    /**
     * @brief Initialize other subsystems
     * @return True if successful
     */
    bool initializeSubsystems();

    /**
     * @brief Load and initialize plugins
     * @return True if successful
     */
    bool initializePlugins();

    /**
     * @brief Shutdown all subsystems in reverse order
     */
    void shutdownSubsystems();

    /**
     * @brief Update all subsystems for one frame
     * @param deltaTime Frame time in seconds
     */
    void updateFrame(float deltaTime);

    /**
     * @brief Render one frame
     */
    void renderFrame();

    /**
     * @brief Handle platform events
     */
    void handleEvents();

private:
    static Engine* instance_;           ///< Singleton instance

    State state_;                       ///< Current engine state
    EngineConfig config_;              ///< Engine configuration
    bool shutdownRequested_;           ///< Shutdown request flag

    UniquePtr<SubsystemManager> subsystemManager_;  ///< Subsystem manager
    UniquePtr<GameLoop> gameLoop_;                  ///< Game loop manager

    // Core subsystems
    UniquePtr<JobSystem> jobSystem_;
    UniquePtr<MemoryManager> memoryManager_;
    UniquePtr<PluginManager> pluginManager_;

    // Engine subsystems
    IRenderer* renderer_;
    InputManager* inputManager_;
    AudioEngine* audioEngine_;
    PhysicsWorld* physicsWorld_;
    SceneManager* sceneManager_;
};

} // namespace PlaytimeEngine
