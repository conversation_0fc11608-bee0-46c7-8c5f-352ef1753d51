#pragma once

/**
 * @file PoolAllocator.h
 * @brief Pool allocator for fixed-size objects
 */

#include "IAllocator.h"
#include <mutex>

namespace PlaytimeEngine {

/**
 * @brief Pool allocator for efficient allocation of fixed-size objects
 */
class ENGINE_API PoolAllocator : public IAllocator {
public:
    /**
     * @brief Constructor
     * @param objectSize Size of each object
     * @param objectCount Number of objects in the pool
     * @param alignment Alignment requirement
     */
    PoolAllocator(size_t objectSize, uint32 objectCount, size_t alignment = sizeof(void*));
    
    /**
     * @brief Destructor
     */
    ~PoolAllocator();
    
    // IAllocator interface
    void* allocate(size_t size, size_t alignment = sizeof(void*)) override;
    void deallocate(void* ptr) override;
    
    size_t getTotalSize() const override { return totalSize_; }
    size_t getUsedSize() const override { return usedCount_ * objectSize_; }
    uint32 getAllocationCount() const override { return usedCount_; }
    bool owns(void* ptr) const override;
    const char* getName() const override { return "PoolAllocator"; }
    
    Stats getStats() const override;
    void reset() override;
    void dump() const override;
    
    /**
     * @brief Get object size
     */
    size_t getObjectSize() const { return objectSize_; }
    
    /**
     * @brief Get object count
     */
    uint32 getObjectCount() const { return objectCount_; }
    
    /**
     * @brief Get free object count
     */
    uint32 getFreeCount() const { return objectCount_ - usedCount_; }
    
    /**
     * @brief Check if pool is full
     */
    bool isFull() const { return usedCount_ >= objectCount_; }
    
    /**
     * @brief Check if pool is empty
     */
    bool isEmpty() const { return usedCount_ == 0; }

private:
    /**
     * @brief Initialize the free list
     */
    void initializeFreeList();
    
    /**
     * @brief Get next free block
     */
    void* getNextFree();
    
    /**
     * @brief Return block to free list
     */
    void returnToFreeList(void* ptr);

private:
    void* memory_;              ///< Pool memory
    void* freeHead_;           ///< Head of free list
    size_t objectSize_;        ///< Size of each object
    uint32 objectCount_;       ///< Total number of objects
    uint32 usedCount_;         ///< Number of used objects
    size_t alignment_;         ///< Alignment requirement
    size_t totalSize_;         ///< Total pool size
    mutable std::mutex mutex_; ///< Thread safety
};

/**
 * @brief Template pool allocator for type-safe allocation
 */
template<typename T>
class TypedPoolAllocator : public PoolAllocator {
public:
    /**
     * @brief Constructor
     * @param objectCount Number of objects in the pool
     */
    explicit TypedPoolAllocator(uint32 objectCount)
        : PoolAllocator(sizeof(T), objectCount, alignof(T)) {}
    
    /**
     * @brief Allocate object
     */
    T* allocate() {
        return static_cast<T*>(PoolAllocator::allocate(sizeof(T), alignof(T)));
    }
    
    /**
     * @brief Deallocate object
     */
    void deallocate(T* ptr) {
        PoolAllocator::deallocate(ptr);
    }
    
    /**
     * @brief Construct object in place
     */
    template<typename... Args>
    T* construct(Args&&... args) {
        T* ptr = allocate();
        if (ptr) {
            new (ptr) T(std::forward<Args>(args)...);
        }
        return ptr;
    }
    
    /**
     * @brief Destruct and deallocate object
     */
    void destruct(T* ptr) {
        if (ptr) {
            ptr->~T();
            deallocate(ptr);
        }
    }
};

} // namespace PlaytimeEngine
