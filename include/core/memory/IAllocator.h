#pragma once

/**
 * @file IAllocator.h
 * @brief Base allocator interface
 */

#include "../Types.h"

namespace PlaytimeEngine {

/**
 * @brief Base interface for all allocators
 */
class ENGINE_API IAllocator : public NonCopyable {
public:
    virtual ~IAllocator() = default;
    
    /**
     * @brief Allocate memory with alignment
     * @param size Size in bytes
     * @param alignment Alignment in bytes (must be power of 2)
     * @return Pointer to allocated memory, or nullptr on failure
     */
    virtual void* allocate(size_t size, size_t alignment = sizeof(void*)) = 0;
    
    /**
     * @brief Deallocate memory
     * @param ptr Pointer to memory to deallocate
     */
    virtual void deallocate(void* ptr) = 0;
    
    /**
     * @brief Get the total size of the allocator
     * @return Total size in bytes
     */
    virtual size_t getTotalSize() const = 0;
    
    /**
     * @brief Get the used size
     * @return Used size in bytes
     */
    virtual size_t getUsedSize() const = 0;
    
    /**
     * @brief Get the free size
     * @return Free size in bytes
     */
    virtual size_t getFreeSize() const { return getTotalSize() - getUsedSize(); }
    
    /**
     * @brief Get the number of allocations
     * @return Number of active allocations
     */
    virtual uint32 getAllocationCount() const = 0;
    
    /**
     * @brief Reset the allocator (if supported)
     */
    virtual void reset() {}
    
    /**
     * @brief Check if the allocator owns a pointer
     * @param ptr Pointer to check
     * @return True if the allocator owns the pointer
     */
    virtual bool owns(void* ptr) const = 0;
    
    /**
     * @brief Get allocator name for debugging
     * @return Allocator name
     */
    virtual const char* getName() const = 0;
    
    /**
     * @brief Get allocator statistics
     */
    struct Stats {
        size_t totalSize = 0;
        size_t usedSize = 0;
        size_t freeSize = 0;
        uint32 allocationCount = 0;
        uint32 totalAllocations = 0;
        uint32 totalDeallocations = 0;
        size_t peakUsage = 0;
        size_t largestAllocation = 0;
        size_t smallestAllocation = SIZE_MAX;
    };
    
    /**
     * @brief Get detailed statistics
     */
    virtual Stats getStats() const = 0;
    
    /**
     * @brief Dump allocator state for debugging
     */
    virtual void dump() const {}

protected:
    /**
     * @brief Update statistics on allocation
     */
    void updateStatsOnAllocation(size_t size) {
        stats_.totalAllocations++;
        stats_.usedSize += size;
        if (stats_.usedSize > stats_.peakUsage) {
            stats_.peakUsage = stats_.usedSize;
        }
        if (size > stats_.largestAllocation) {
            stats_.largestAllocation = size;
        }
        if (size < stats_.smallestAllocation) {
            stats_.smallestAllocation = size;
        }
    }
    
    /**
     * @brief Update statistics on deallocation
     */
    void updateStatsOnDeallocation(size_t size) {
        stats_.totalDeallocations++;
        stats_.usedSize -= size;
    }

protected:
    mutable Stats stats_;
};

/**
 * @brief Null allocator that always fails
 */
class ENGINE_API NullAllocator : public IAllocator {
public:
    void* allocate(size_t size, size_t alignment = sizeof(void*)) override {
        (void)size; (void)alignment;
        return nullptr;
    }
    
    void deallocate(void* ptr) override {
        (void)ptr;
    }
    
    size_t getTotalSize() const override { return 0; }
    size_t getUsedSize() const override { return 0; }
    uint32 getAllocationCount() const override { return 0; }
    bool owns(void* ptr) const override { (void)ptr; return false; }
    const char* getName() const override { return "NullAllocator"; }
    
    Stats getStats() const override { return Stats{}; }
};

/**
 * @brief Malloc allocator that uses system malloc/free
 */
class ENGINE_API MallocAllocator : public IAllocator {
public:
    MallocAllocator();
    ~MallocAllocator();
    
    void* allocate(size_t size, size_t alignment = sizeof(void*)) override;
    void deallocate(void* ptr) override;
    
    size_t getTotalSize() const override { return SIZE_MAX; }
    size_t getUsedSize() const override;
    uint32 getAllocationCount() const override;
    bool owns(void* ptr) const override;
    const char* getName() const override { return "MallocAllocator"; }
    
    Stats getStats() const override;

private:
    struct AllocationHeader {
        size_t size;
        uint32 magic;
    };
    
    static constexpr uint32 MAGIC = 0xDEADBEEF;
    mutable std::mutex mutex_;
    uint32 allocationCount_;
    size_t totalAllocated_;
};

} // namespace PlaytimeEngine
