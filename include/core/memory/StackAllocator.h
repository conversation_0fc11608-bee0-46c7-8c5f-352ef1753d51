#pragma once

/**
 * @file StackAllocator.h
 * @brief Stack allocator for temporary allocations
 */

#include "IAllocator.h"
#include <mutex>

namespace PlaytimeEngine {

/**
 * @brief Stack allocator for fast temporary allocations
 */
class ENGINE_API StackAllocator : public IAllocator {
public:
    /**
     * @brief Constructor
     * @param size Total size of the stack
     */
    explicit StackAllocator(size_t size);
    
    /**
     * @brief Destructor
     */
    ~StackAllocator();
    
    // IAllocator interface
    void* allocate(size_t size, size_t alignment = sizeof(void*)) override;
    void deallocate(void* ptr) override;
    
    size_t getTotalSize() const override { return totalSize_; }
    size_t getUsedSize() const override { return currentOffset_; }
    uint32 getAllocationCount() const override { return allocationCount_; }
    bool owns(void* ptr) const override;
    const char* getName() const override { return "StackAllocator"; }
    
    Stats getStats() const override;
    void reset() override;
    void dump() const override;
    
    /**
     * @brief Stack marker for scoped allocations
     */
    class Marker {
    public:
        Marker(StackAllocator* allocator) : allocator_(allocator), offset_(allocator->currentOffset_) {}
        ~Marker() { allocator_->rewindToMarker(*this); }
        
        Marker(const Marker&) = delete;
        Marker& operator=(const Marker&) = delete;
        
        Marker(Marker&& other) noexcept : allocator_(other.allocator_), offset_(other.offset_) {
            other.allocator_ = nullptr;
        }
        
        Marker& operator=(Marker&& other) noexcept {
            if (this != &other) {
                allocator_ = other.allocator_;
                offset_ = other.offset_;
                other.allocator_ = nullptr;
            }
            return *this;
        }
        
    private:
        friend class StackAllocator;
        StackAllocator* allocator_;
        size_t offset_;
    };
    
    /**
     * @brief Create a marker for the current stack position
     */
    Marker createMarker() { return Marker(this); }
    
    /**
     * @brief Rewind to a specific marker
     */
    void rewindToMarker(const Marker& marker);
    
    /**
     * @brief Get current stack top
     */
    size_t getCurrentOffset() const { return currentOffset_; }
    
    /**
     * @brief Get remaining space
     */
    size_t getRemainingSpace() const { return totalSize_ - currentOffset_; }

private:
    /**
     * @brief Allocation header for tracking
     */
    struct AllocationHeader {
        size_t size;
        size_t prevOffset;
    };
    
    /**
     * @brief Align offset to required alignment
     */
    size_t alignOffset(size_t offset, size_t alignment) const;

private:
    void* memory_;                 ///< Stack memory
    size_t totalSize_;            ///< Total stack size
    size_t currentOffset_;        ///< Current stack top
    uint32 allocationCount_;      ///< Number of active allocations
    mutable std::mutex mutex_;    ///< Thread safety
};

/**
 * @brief Scoped stack allocator for RAII
 */
class ENGINE_API ScopedStackAllocator {
public:
    /**
     * @brief Constructor
     * @param allocator Stack allocator to use
     */
    explicit ScopedStackAllocator(StackAllocator* allocator)
        : marker_(allocator->createMarker()) {}
    
    /**
     * @brief Destructor automatically rewinds the stack
     */
    ~ScopedStackAllocator() = default;
    
    // Non-copyable, movable
    ScopedStackAllocator(const ScopedStackAllocator&) = delete;
    ScopedStackAllocator& operator=(const ScopedStackAllocator&) = delete;
    
    ScopedStackAllocator(ScopedStackAllocator&&) = default;
    ScopedStackAllocator& operator=(ScopedStackAllocator&&) = default;

private:
    StackAllocator::Marker marker_;
};

} // namespace PlaytimeEngine

/**
 * @brief Macro for scoped stack allocation
 */
#define SCOPED_STACK_ALLOC(allocator) \
    PlaytimeEngine::ScopedStackAllocator CONCAT(_scopedAlloc, __LINE__)(allocator)
