#pragma once

/**
 * @file Memory.h
 * @brief Memory management system interface
 */

#include "Types.h"
#include "memory/IAllocator.h"
#include "memory/PoolAllocator.h"
#include "memory/StackAllocator.h"
#include "memory/LinearAllocator.h"
#include "memory/MemoryTracker.h"

namespace PlaytimeEngine {

/**
 * @brief Memory manager for the engine
 */
class ENGINE_API MemoryManager : public NonCopyable {
public:
    /**
     * @brief Memory statistics
     */
    struct Stats {
        size_t totalAllocated = 0;
        size_t totalFreed = 0;
        size_t currentUsage = 0;
        size_t peakUsage = 0;
        uint32 allocationCount = 0;
        uint32 freeCount = 0;
    };

    MemoryManager();
    ~MemoryManager();
    
    /**
     * @brief Initialize the memory manager
     */
    bool initialize(size_t heapSize = 256 * MEGABYTE);
    
    /**
     * @brief Shutdown the memory manager
     */
    void shutdown();
    
    /**
     * @brief Get the default allocator
     */
    IAllocator* getDefaultAllocator() const { return defaultAllocator_; }
    
    /**
     * @brief Get a pool allocator for specific size
     */
    PoolAllocator* getPoolAllocator(size_t objectSize);
    
    /**
     * @brief Get a stack allocator
     */
    StackAllocator* getStackAllocator(size_t size);
    
    /**
     * @brief Get a linear allocator
     */
    LinearAllocator* getLinearAllocator(size_t size);
    
    /**
     * @brief Get memory statistics
     */
    Stats getStats() const;
    
    /**
     * @brief Reset statistics
     */
    void resetStats();
    
    /**
     * @brief Enable/disable memory tracking
     */
    void setTrackingEnabled(bool enabled);
    
    /**
     * @brief Check if memory tracking is enabled
     */
    bool isTrackingEnabled() const;
    
    /**
     * @brief Dump memory leaks (debug only)
     */
    void dumpLeaks() const;

private:
    IAllocator* defaultAllocator_;
    MemoryTracker* tracker_;
    bool initialized_;
};

/**
 * @brief Global memory functions
 */
namespace Memory {
    /**
     * @brief Initialize global memory system
     */
    ENGINE_API bool initialize(size_t heapSize = 256 * MEGABYTE);
    
    /**
     * @brief Shutdown global memory system
     */
    ENGINE_API void shutdown();
    
    /**
     * @brief Get the global memory manager
     */
    ENGINE_API MemoryManager* getManager();
    
    /**
     * @brief Allocate memory with alignment
     */
    ENGINE_API void* allocate(size_t size, size_t alignment = sizeof(void*), IAllocator* allocator = nullptr);
    
    /**
     * @brief Free memory
     */
    ENGINE_API void deallocate(void* ptr, IAllocator* allocator = nullptr);
    
    /**
     * @brief Allocate and construct object
     */
    template<typename T, typename... Args>
    T* construct(IAllocator* allocator, Args&&... args) {
        void* ptr = allocate(sizeof(T), alignof(T), allocator);
        return new (ptr) T(std::forward<Args>(args)...);
    }
    
    /**
     * @brief Destruct and deallocate object
     */
    template<typename T>
    void destruct(T* ptr, IAllocator* allocator = nullptr) {
        if (ptr) {
            ptr->~T();
            deallocate(ptr, allocator);
        }
    }
    
    /**
     * @brief Allocate array
     */
    template<typename T>
    T* allocateArray(size_t count, IAllocator* allocator = nullptr) {
        return static_cast<T*>(allocate(sizeof(T) * count, alignof(T), allocator));
    }
    
    /**
     * @brief Deallocate array
     */
    template<typename T>
    void deallocateArray(T* ptr, size_t count, IAllocator* allocator = nullptr) {
        if (ptr) {
            for (size_t i = 0; i < count; ++i) {
                ptr[i].~T();
            }
            deallocate(ptr, allocator);
        }
    }
}

/**
 * @brief Custom new/delete operators for specific allocators
 */
#define DECLARE_ALLOCATOR_NEW_DELETE(AllocatorType) \
    void* operator new(size_t size, AllocatorType* allocator) { \
        return allocator->allocate(size, alignof(std::max_align_t)); \
    } \
    void* operator new[](size_t size, AllocatorType* allocator) { \
        return allocator->allocate(size, alignof(std::max_align_t)); \
    } \
    void operator delete(void* ptr, AllocatorType* allocator) { \
        allocator->deallocate(ptr); \
    } \
    void operator delete[](void* ptr, AllocatorType* allocator) { \
        allocator->deallocate(ptr); \
    }

} // namespace PlaytimeEngine

/**
 * @brief Memory allocation macros
 */
#define ENGINE_NEW(type, allocator, ...) \
    PlaytimeEngine::Memory::construct<type>(allocator, ##__VA_ARGS__)

#define ENGINE_DELETE(ptr, allocator) \
    PlaytimeEngine::Memory::destruct(ptr, allocator)

#define ENGINE_NEW_ARRAY(type, count, allocator) \
    PlaytimeEngine::Memory::allocateArray<type>(count, allocator)

#define ENGINE_DELETE_ARRAY(ptr, count, allocator) \
    PlaytimeEngine::Memory::deallocateArray(ptr, count, allocator)
