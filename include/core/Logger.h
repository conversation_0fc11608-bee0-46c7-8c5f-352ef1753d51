#pragma once

/**
 * @file Logger.h
 * @brief Logging system for the engine
 */

#include "Types.h"
#include <fstream>
#include <mutex>

namespace PlaytimeEngine {

/**
 * @brief Log levels
 */
enum class LogLevel : uint32 {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warning = 3,
    Error = 4,
    Critical = 5
};

/**
 * @brief Logger class for thread-safe logging
 */
class ENGINE_API Logger : public NonCopyable {
public:
    static Logger& getInstance();
    
    /**
     * @brief Initialize the logger
     */
    bool initialize(const String& filename = "", LogLevel level = LogLevel::Info);
    
    /**
     * @brief Shutdown the logger
     */
    void shutdown();
    
    /**
     * @brief Set log level
     */
    void setLevel(LogLevel level) { level_ = level; }
    
    /**
     * @brief Get log level
     */
    LogLevel getLevel() const { return level_; }
    
    /**
     * @brief Log a message
     */
    void log(LogLevel level, const char* format, ...);
    
    /**
     * @brief Log a message with file/line info
     */
    void logWithLocation(LogLevel level, const char* file, int line, const char* format, ...);
    
    /**
     * @brief Flush log output
     */
    void flush();

private:
    Logger() = default;
    ~Logger() = default;
    
    void writeLog(LogLevel level, const String& message);
    const char* getLevelString(LogLevel level);

private:
    LogLevel level_ = LogLevel::Info;
    std::ofstream fileStream_;
    std::mutex mutex_;
    bool initialized_ = false;
};

// Convenience macros
#define LOG_TRACE(format, ...) \
    PlaytimeEngine::Logger::getInstance().logWithLocation(PlaytimeEngine::LogLevel::Trace, __FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_DEBUG(format, ...) \
    PlaytimeEngine::Logger::getInstance().logWithLocation(PlaytimeEngine::LogLevel::Debug, __FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_INFO(format, ...) \
    PlaytimeEngine::Logger::getInstance().logWithLocation(PlaytimeEngine::LogLevel::Info, __FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_WARNING(format, ...) \
    PlaytimeEngine::Logger::getInstance().logWithLocation(PlaytimeEngine::LogLevel::Warning, __FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_ERROR(format, ...) \
    PlaytimeEngine::Logger::getInstance().logWithLocation(PlaytimeEngine::LogLevel::Error, __FILE__, __LINE__, format, ##__VA_ARGS__)

#define LOG_CRITICAL(format, ...) \
    PlaytimeEngine::Logger::getInstance().logWithLocation(PlaytimeEngine::LogLevel::Critical, __FILE__, __LINE__, format, ##__VA_ARGS__)

} // namespace PlaytimeEngine
