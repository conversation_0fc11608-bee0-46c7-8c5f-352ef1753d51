#pragma once

/**
 * @file Application.h
 * @brief Base application class for engine users
 */

#include "Types.h"
#include "EngineConfig.h"

namespace PlaytimeEngine {

// Forward declarations
class Engine;

/**
 * @brief Base application class that users inherit from
 */
class ENGINE_API Application : public NonCopyable {
public:
    Application();
    virtual ~Application();
    
    /**
     * @brief Initialize the application
     * @return True if successful
     */
    virtual bool initialize() = 0;
    
    /**
     * @brief Update the application logic
     * @param deltaTime Frame time in seconds
     */
    virtual void update(float deltaTime) = 0;
    
    /**
     * @brief Render the application
     */
    virtual void render() = 0;
    
    /**
     * @brief Shutdown the application
     */
    virtual void shutdown() = 0;
    
    /**
     * @brief Handle application events
     */
    virtual void handleEvents() {}
    
    /**
     * @brief Get the application name
     */
    virtual const char* getName() const = 0;
    
    /**
     * @brief Get the engine configuration
     */
    virtual EngineConfig getEngineConfig() const;
    
    /**
     * @brief Run the application
     * @return Exit code
     */
    int run();
    
    /**
     * @brief Request application exit
     */
    void requestExit() { exitRequested_ = true; }
    
    /**
     * @brief Check if exit was requested
     */
    bool isExitRequested() const { return exitRequested_; }
    
    /**
     * @brief Get the engine instance
     */
    Engine* getEngine() const { return engine_; }

protected:
    /**
     * @brief Called when the application is about to exit
     */
    virtual void onExit() {}
    
    /**
     * @brief Called when the application is paused
     */
    virtual void onPause() {}
    
    /**
     * @brief Called when the application is resumed
     */
    virtual void onResume() {}

private:
    Engine* engine_;
    bool exitRequested_;
    bool initialized_;
};

/**
 * @brief Macro to define the main application entry point
 */
#define IMPLEMENT_APPLICATION(AppClass) \
    int main(int argc, char* argv[]) { \
        (void)argc; (void)argv; \
        AppClass app; \
        return app.run(); \
    }

} // namespace PlaytimeEngine
