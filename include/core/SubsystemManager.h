#pragma once

/**
 * @file SubsystemManager.h
 * @brief Subsystem management and dependency injection
 */

#include "Types.h"
#include <unordered_map>
#include <vector>
#include <typeindex>

namespace PlaytimeEngine {

/**
 * @brief Base class for all engine subsystems
 */
class ENGINE_API ISubsystem : public NonCopyable {
public:
    virtual ~ISubsystem() = default;
    
    /**
     * @brief Initialize the subsystem
     */
    virtual bool initialize() = 0;
    
    /**
     * @brief Update the subsystem
     */
    virtual void update(float deltaTime) = 0;
    
    /**
     * @brief Shutdown the subsystem
     */
    virtual void shutdown() = 0;
    
    /**
     * @brief Get subsystem name
     */
    virtual const char* getName() const = 0;
    
    /**
     * @brief Get subsystem dependencies
     */
    virtual std::vector<std::type_index> getDependencies() const { return {}; }
};

/**
 * @brief Manages all engine subsystems with dependency injection
 */
class ENGINE_API SubsystemManager : public NonCopyable {
public:
    SubsystemManager();
    ~SubsystemManager();
    
    /**
     * @brief Register a subsystem
     */
    template<typename T>
    void registerSubsystem(UniquePtr<T> subsystem) {
        static_assert(std::is_base_of_v<ISubsystem, T>, "T must inherit from ISubsystem");
        
        std::type_index typeIndex = std::type_index(typeid(T));
        subsystems_[typeIndex] = std::move(subsystem);
        initializationOrder_.push_back(typeIndex);
    }
    
    /**
     * @brief Get a subsystem by type
     */
    template<typename T>
    T* getSubsystem() {
        static_assert(std::is_base_of_v<ISubsystem, T>, "T must inherit from ISubsystem");
        
        std::type_index typeIndex = std::type_index(typeid(T));
        auto it = subsystems_.find(typeIndex);
        if (it != subsystems_.end()) {
            return static_cast<T*>(it->second.get());
        }
        return nullptr;
    }
    
    /**
     * @brief Initialize all subsystems in dependency order
     */
    bool initializeAll();
    
    /**
     * @brief Update all subsystems
     */
    void updateAll(float deltaTime);
    
    /**
     * @brief Shutdown all subsystems in reverse order
     */
    void shutdownAll();
    
    /**
     * @brief Check if a subsystem is registered
     */
    template<typename T>
    bool hasSubsystem() const {
        std::type_index typeIndex = std::type_index(typeid(T));
        return subsystems_.find(typeIndex) != subsystems_.end();
    }
    
    /**
     * @brief Get number of registered subsystems
     */
    size_t getSubsystemCount() const { return subsystems_.size(); }

private:
    /**
     * @brief Resolve subsystem dependencies
     */
    bool resolveDependencies();
    
    /**
     * @brief Topological sort for dependency resolution
     */
    std::vector<std::type_index> topologicalSort();

private:
    std::unordered_map<std::type_index, UniquePtr<ISubsystem>> subsystems_;
    std::vector<std::type_index> initializationOrder_;
    bool initialized_;
};

} // namespace PlaytimeEngine
