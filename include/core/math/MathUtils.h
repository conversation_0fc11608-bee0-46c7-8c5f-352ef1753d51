#pragma once

/**
 * @file MathUtils.h
 * @brief Mathematical utility functions
 */

#include "../Types.h"
#include <cmath>
#include <algorithm>

namespace PlaytimeEngine {
namespace Math {

/**
 * @brief Linear interpolation
 */
template<typename T>
constexpr T lerp(const T& a, const T& b, float t) {
    return a + t * (b - a);
}

/**
 * @brief Clamp value between min and max
 */
template<typename T>
constexpr T clamp(const T& value, const T& min, const T& max) {
    return std::max(min, std::min(max, value));
}

/**
 * @brief Clamp value between 0 and 1
 */
template<typename T>
constexpr T saturate(const T& value) {
    return clamp(value, T(0), T(1));
}

/**
 * @brief Get minimum of two values
 */
template<typename T>
constexpr T min(const T& a, const T& b) {
    return std::min(a, b);
}

/**
 * @brief Get maximum of two values
 */
template<typename T>
constexpr T max(const T& a, const T& b) {
    return std::max(a, b);
}

/**
 * @brief Get absolute value
 */
template<typename T>
constexpr T abs(const T& value) {
    return value < T(0) ? -value : value;
}

/**
 * @brief Get sign of value (-1, 0, or 1)
 */
template<typename T>
constexpr T sign(const T& value) {
    return (T(0) < value) - (value < T(0));
}

/**
 * @brief Check if value is approximately zero
 */
template<typename T>
constexpr bool isZero(const T& value, const T& epsilon = T(EPSILON)) {
    return abs(value) <= epsilon;
}

/**
 * @brief Check if two values are approximately equal
 */
template<typename T>
constexpr bool isEqual(const T& a, const T& b, const T& epsilon = T(EPSILON)) {
    return abs(a - b) <= epsilon;
}

/**
 * @brief Square a value
 */
template<typename T>
constexpr T square(const T& value) {
    return value * value;
}

/**
 * @brief Cube a value
 */
template<typename T>
constexpr T cube(const T& value) {
    return value * value * value;
}

/**
 * @brief Fast inverse square root (Quake algorithm)
 */
ENGINE_API float fastInverseSqrt(float x);

/**
 * @brief Convert degrees to radians
 */
constexpr float toRadians(float degrees) {
    return degrees * DEG_TO_RAD;
}

/**
 * @brief Convert radians to degrees
 */
constexpr float toDegrees(float radians) {
    return radians * RAD_TO_DEG;
}

/**
 * @brief Wrap angle to [-PI, PI] range
 */
ENGINE_API float wrapAngle(float angle);

/**
 * @brief Wrap angle to [0, 2*PI] range
 */
ENGINE_API float wrapAngle2Pi(float angle);

/**
 * @brief Smooth step function (3t² - 2t³)
 */
constexpr float smoothStep(float t) {
    t = saturate(t);
    return t * t * (3.0f - 2.0f * t);
}

/**
 * @brief Smoother step function (6t⁵ - 15t⁴ + 10t³)
 */
constexpr float smootherStep(float t) {
    t = saturate(t);
    return t * t * t * (t * (t * 6.0f - 15.0f) + 10.0f);
}

/**
 * @brief Hermite interpolation
 */
constexpr float hermite(float t) {
    return t * t * (3.0f - 2.0f * t);
}

/**
 * @brief Quintic interpolation
 */
constexpr float quintic(float t) {
    return t * t * t * (t * (t * 6.0f - 15.0f) + 10.0f);
}

/**
 * @brief Power function with integer exponent
 */
template<typename T>
constexpr T pow(const T& base, int exponent) {
    if (exponent == 0) return T(1);
    if (exponent == 1) return base;
    if (exponent < 0) return T(1) / pow(base, -exponent);
    
    T result = T(1);
    T current = base;
    while (exponent > 0) {
        if (exponent & 1) {
            result *= current;
        }
        current *= current;
        exponent >>= 1;
    }
    return result;
}

/**
 * @brief Check if a number is power of 2
 */
template<typename T>
constexpr bool isPowerOfTwo(T value) {
    return value > 0 && (value & (value - 1)) == 0;
}

/**
 * @brief Round up to next power of 2
 */
ENGINE_API uint32 nextPowerOfTwo(uint32 value);
ENGINE_API uint64 nextPowerOfTwo(uint64 value);

/**
 * @brief Round down to previous power of 2
 */
ENGINE_API uint32 prevPowerOfTwo(uint32 value);
ENGINE_API uint64 prevPowerOfTwo(uint64 value);

/**
 * @brief Count leading zeros
 */
ENGINE_API uint32 countLeadingZeros(uint32 value);
ENGINE_API uint32 countLeadingZeros(uint64 value);

/**
 * @brief Count trailing zeros
 */
ENGINE_API uint32 countTrailingZeros(uint32 value);
ENGINE_API uint32 countTrailingZeros(uint64 value);

/**
 * @brief Count set bits (population count)
 */
ENGINE_API uint32 popCount(uint32 value);
ENGINE_API uint32 popCount(uint64 value);

/**
 * @brief Bit reverse
 */
ENGINE_API uint32 bitReverse(uint32 value);
ENGINE_API uint64 bitReverse(uint64 value);

/**
 * @brief Random number generation utilities
 */
namespace Random {
    /**
     * @brief Set random seed
     */
    ENGINE_API void setSeed(uint32 seed);
    
    /**
     * @brief Generate random float [0, 1)
     */
    ENGINE_API float randomFloat();
    
    /**
     * @brief Generate random float [min, max)
     */
    ENGINE_API float randomFloat(float min, float max);
    
    /**
     * @brief Generate random integer [min, max]
     */
    ENGINE_API int randomInt(int min, int max);
    
    /**
     * @brief Generate random boolean
     */
    ENGINE_API bool randomBool();
}

} // namespace Math
} // namespace PlaytimeEngine
