#pragma once

/**
 * @file Matrix.h
 * @brief Matrix mathematics classes for 3x3 and 4x4 transformations
 * 
 * Provides high-performance matrix classes with SIMD optimization
 * for common game engine transformation operations.
 */

#include "Vector.h"
#include <cstring>

namespace PlaytimeEngine {
namespace Math {

/**
 * @brief 3x3 Matrix class for 2D transformations and rotations
 */
class Matrix3x3 {
public:
    union {
        float m[9];           // Linear array
        float m2d[3][3];      // 2D array
        struct {
            float m00, m01, m02;
            float m10, m11, m12;
            float m20, m21, m22;
        };
    };

    // Constructors
    Matrix3x3();
    Matrix3x3(float m00, float m01, float m02,
              float m10, float m11, float m12,
              float m20, float m21, float m22);
    explicit Matrix3x3(const float* data);

    // Copy and assignment
    Matrix3x3(const Matrix3x3& other) = default;
    Matrix3x3& operator=(const Matrix3x3& other) = default;

    // Array access
    float& operator[](int index) {
        assert(index >= 0 && index < 9);
        return m[index];
    }
    
    const float& operator[](int index) const {
        assert(index >= 0 && index < 9);
        return m[index];
    }

    float& operator()(int row, int col) {
        assert(row >= 0 && row < 3 && col >= 0 && col < 3);
        return m2d[row][col];
    }
    
    const float& operator()(int row, int col) const {
        assert(row >= 0 && row < 3 && col >= 0 && col < 3);
        return m2d[row][col];
    }

    // Arithmetic operators
    Matrix3x3 operator+(const Matrix3x3& other) const;
    Matrix3x3 operator-(const Matrix3x3& other) const;
    Matrix3x3 operator*(const Matrix3x3& other) const;
    Matrix3x3 operator*(float scalar) const;
    Matrix3x3 operator/(float scalar) const;

    // Assignment operators
    Matrix3x3& operator+=(const Matrix3x3& other);
    Matrix3x3& operator-=(const Matrix3x3& other);
    Matrix3x3& operator*=(const Matrix3x3& other);
    Matrix3x3& operator*=(float scalar);
    Matrix3x3& operator/=(float scalar);

    // Comparison operators
    bool operator==(const Matrix3x3& other) const;
    bool operator!=(const Matrix3x3& other) const { return !(*this == other); }

    // Vector multiplication
    Vector3 operator*(const Vector3& vector) const;
    Vector2 transformPoint(const Vector2& point) const;
    Vector2 transformVector(const Vector2& vector) const;

    // Matrix operations
    Matrix3x3 transposed() const;
    Matrix3x3& transpose();
    Matrix3x3 inverse() const;
    Matrix3x3& invert();
    float determinant() const;
    float trace() const;

    // Row and column access
    Vector3 getRow(int row) const;
    Vector3 getColumn(int col) const;
    void setRow(int row, const Vector3& vector);
    void setColumn(int col, const Vector3& vector);

    // Static factory methods
    static Matrix3x3 identity();
    static Matrix3x3 zero();
    static Matrix3x3 scale(float sx, float sy);
    static Matrix3x3 scale(const Vector2& scale);
    static Matrix3x3 rotation(float angle);
    static Matrix3x3 translation(float tx, float ty);
    static Matrix3x3 translation(const Vector2& translation);
    static Matrix3x3 fromEulerAngles(float pitch, float yaw, float roll);
    static Matrix3x3 lookAt(const Vector3& forward, const Vector3& up);

    // Constants
    static const Matrix3x3 IDENTITY;
    static const Matrix3x3 ZERO;
};

/**
 * @brief 4x4 Matrix class for 3D transformations with SIMD optimization
 */
class Matrix4x4 {
public:
    union {
        float m[16];          // Linear array
        float m2d[4][4];      // 2D array
        struct {
            float m00, m01, m02, m03;
            float m10, m11, m12, m13;
            float m20, m21, m22, m23;
            float m30, m31, m32, m33;
        };
#ifdef __SSE__
        __m128 rows[4];       // SIMD rows
#endif
    };

    // Constructors
    Matrix4x4();
    Matrix4x4(float m00, float m01, float m02, float m03,
              float m10, float m11, float m12, float m13,
              float m20, float m21, float m22, float m23,
              float m30, float m31, float m32, float m33);
    explicit Matrix4x4(const float* data);
    explicit Matrix4x4(const Matrix3x3& mat3);

#ifdef __SSE__
    Matrix4x4(__m128 row0, __m128 row1, __m128 row2, __m128 row3);
#endif

    // Copy and assignment
    Matrix4x4(const Matrix4x4& other) = default;
    Matrix4x4& operator=(const Matrix4x4& other) = default;

    // Array access
    float& operator[](int index) {
        assert(index >= 0 && index < 16);
        return m[index];
    }
    
    const float& operator[](int index) const {
        assert(index >= 0 && index < 16);
        return m[index];
    }

    float& operator()(int row, int col) {
        assert(row >= 0 && row < 4 && col >= 0 && col < 4);
        return m2d[row][col];
    }
    
    const float& operator()(int row, int col) const {
        assert(row >= 0 && row < 4 && col >= 0 && col < 4);
        return m2d[row][col];
    }

    // Arithmetic operators (SIMD optimized)
    Matrix4x4 operator+(const Matrix4x4& other) const;
    Matrix4x4 operator-(const Matrix4x4& other) const;
    Matrix4x4 operator*(const Matrix4x4& other) const;
    Matrix4x4 operator*(float scalar) const;
    Matrix4x4 operator/(float scalar) const;

    // Assignment operators
    Matrix4x4& operator+=(const Matrix4x4& other);
    Matrix4x4& operator-=(const Matrix4x4& other);
    Matrix4x4& operator*=(const Matrix4x4& other);
    Matrix4x4& operator*=(float scalar);
    Matrix4x4& operator/=(float scalar);

    // Comparison operators
    bool operator==(const Matrix4x4& other) const;
    bool operator!=(const Matrix4x4& other) const { return !(*this == other); }

    // Vector multiplication
    Vector4 operator*(const Vector4& vector) const;
    Vector3 transformPoint(const Vector3& point) const;
    Vector3 transformVector(const Vector3& vector) const;
    Vector3 transformNormal(const Vector3& normal) const;

    // Matrix operations
    Matrix4x4 transposed() const;
    Matrix4x4& transpose();
    Matrix4x4 inverse() const;
    Matrix4x4& invert();
    float determinant() const;
    float trace() const;

    // Decomposition
    bool decompose(Vector3& translation, Vector3& rotation, Vector3& scale) const;
    Matrix3x3 getRotationMatrix() const;
    Vector3 getTranslation() const;
    Vector3 getScale() const;

    // Row and column access
    Vector4 getRow(int row) const;
    Vector4 getColumn(int col) const;
    void setRow(int row, const Vector4& vector);
    void setColumn(int col, const Vector4& vector);

    // Static factory methods
    static Matrix4x4 identity();
    static Matrix4x4 zero();
    static Matrix4x4 scale(float sx, float sy, float sz);
    static Matrix4x4 scale(const Vector3& scale);
    static Matrix4x4 translation(float tx, float ty, float tz);
    static Matrix4x4 translation(const Vector3& translation);
    static Matrix4x4 rotationX(float angle);
    static Matrix4x4 rotationY(float angle);
    static Matrix4x4 rotationZ(float angle);
    static Matrix4x4 rotation(const Vector3& axis, float angle);
    static Matrix4x4 fromEulerAngles(float pitch, float yaw, float roll);
    static Matrix4x4 fromQuaternion(const class Quaternion& quat);
    
    // View and projection matrices
    static Matrix4x4 lookAt(const Vector3& eye, const Vector3& target, const Vector3& up);
    static Matrix4x4 perspective(float fovy, float aspect, float near, float far);
    static Matrix4x4 perspectiveInfinite(float fovy, float aspect, float near);
    static Matrix4x4 orthographic(float left, float right, float bottom, float top, float near, float far);
    static Matrix4x4 orthographic(float width, float height, float near, float far);
    
    // Transform composition
    static Matrix4x4 trs(const Vector3& translation, const class Quaternion& rotation, const Vector3& scale);
    static Matrix4x4 trs(const Vector3& translation, const Vector3& eulerAngles, const Vector3& scale);

    // Constants
    static const Matrix4x4 IDENTITY;
    static const Matrix4x4 ZERO;
};

// Global operators for scalar multiplication
Matrix3x3 operator*(float scalar, const Matrix3x3& matrix);
Matrix4x4 operator*(float scalar, const Matrix4x4& matrix);

} // namespace Math
} // namespace PlaytimeEngine
