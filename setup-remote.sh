#!/bin/bash

# Setup script for adding a remote repository to Project Playtime Engine
# Usage: ./setup-remote.sh <remote-url>

if [ $# -eq 0 ]; then
    echo "Usage: $0 <remote-url>"
    echo "Example: $0 https://github.com/username/project-playtime-engine.git"
    echo "Example: $0 **************:username/project-playtime-engine.git"
    exit 1
fi

REMOTE_URL=$1

echo "Setting up remote repository..."
echo "Remote URL: $REMOTE_URL"

# Add the remote origin
git remote add origin "$REMOTE_URL"

# Verify the remote was added
echo "Remote repositories:"
git remote -v

echo ""
echo "Remote repository setup complete!"
echo "To push your code to the remote repository, run:"
echo "  git push -u origin main"
echo ""
echo "Note: Make sure you have created the repository on your Git hosting service"
echo "      (GitHub, GitLab, etc.) before pushing."
