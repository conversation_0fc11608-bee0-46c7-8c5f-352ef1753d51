# Project Playtime Engine - AI Agent Development Standards

## Project Overview

- **Target**: Next-generation high-performance game engine supporting 2D, 2.5D, and 3D development
- **Platforms**: Windows (DirectX 12) and Linux (Vulkan) with feature parity
- **Language**: Modern C++ (C++20 or newer)
- **Architecture**: Modular, plugin-based, data-oriented design
- **Build System**: CMake with cross-platform support

## Mandatory Architecture Rules

### Cross-Platform Abstraction
- **MUST** implement platform-specific backends behind unified interfaces
- **MUST** use conditional compilation for platform differences: `#ifdef _WIN32` vs `#else` for Linux
- **MUST** abstract graphics APIs: `DX12Renderer` and `VulkanRenderer` implementing common `IRenderer` interface
- **MUST** use HLSL shaders compiled to both DXIL (DX12) and SPIR-V (Vulkan) via DXC compiler
- **NEVER** expose platform-specific APIs directly to engine core code

### Modular Subsystem Design
- **MUST** organize code into distinct subsystems: Core, Rendering, Physics, Audio, Animation, UI, Scene, Memory, Threading
- **MUST** implement each subsystem as separate modules with clear interfaces
- **MUST** use dependency injection pattern - avoid singletons and global state
- **MUST** support plugin loading via DLLs (Windows) and .so files (Linux)

### Performance Requirements
- **MUST** implement multithreaded command buffer recording for rendering
- **MUST** use job system for CPU parallelism across all subsystems
- **MUST** implement frame pipelining (2-3 frame buffer)
- **MUST** use data-oriented design principles for cache efficiency
- **MUST** implement GPU-driven culling and bindless resources

## File Organization Standards

### Directory Structure
```
/src/
  /core/           - Math, memory, containers, OS abstraction
  /rendering/      - Graphics APIs, shaders, materials
  /physics/        - Physics integration (Jolt/PhysX)
  /audio/          - Audio engine (miniaudio)
  /animation/      - Skeletal and sprite animation
  /scene/          - ECS, scene graph, spatial partitioning
  /ui/             - ImGui integration and UI systems
  /editor/         - Editor-specific code
  /plugins/        - Plugin system and sample plugins
/shaders/          - HLSL shader source files
/assets/           - Sample assets and test content
/tools/            - Build scripts and utilities
/docs/             - Technical documentation
```

### Naming Conventions
- **Classes**: PascalCase (e.g., `RenderDevice`, `PhysicsWorld`)
- **Functions**: camelCase (e.g., `createBuffer`, `updateTransform`)
- **Variables**: camelCase (e.g., `vertexBuffer`, `deltaTime`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_LIGHTS`, `DEFAULT_GRAVITY`)
- **Files**: PascalCase for headers (e.g., `RenderDevice.h`), lowercase for source (e.g., `render_device.cpp`)

## Subsystem Integration Rules

### Rendering Subsystem
- **MUST** implement deferred shading pipeline with PBR materials
- **MUST** support both 2D sprite batching and 3D mesh rendering in unified pipeline
- **MUST** integrate ray tracing for reflections, shadows, and global illumination
- **MUST** implement upscaling support (DLSS, FSR, XeSS) via NVIDIA Streamline
- **MUST** use render graph system for pass management and resource dependencies

### Physics Integration
- **MUST** use Jolt Physics as primary backend with PhysX as optional GPU acceleration
- **MUST** support both 2D (Box2D-style) and 3D physics simulation
- **MUST** run physics on fixed timestep (60Hz or 120Hz) with accumulation
- **MUST** implement deterministic mode for networking compatibility
- **MUST** sync physics transforms with ECS components via job system

### Animation System
- **MUST** support both skeletal animation (3D) and sprite animation (2D)
- **MUST** implement GPU skinning for skeletal meshes
- **MUST** use ozz-animation or equivalent for compression and performance
- **MUST** support animation blending, state machines, and IK solvers
- **MUST** integrate with physics for ragdoll transitions

## Multi-File Coordination Requirements

### When modifying README.md:
- **MUST** update guide.md if architectural changes are described
- **MUST** verify consistency with project documentation

### When adding new subsystems:
- **MUST** update CMakeLists.txt with new source files
- **MUST** add corresponding interface headers to /include/
- **MUST** update plugin system registration if subsystem is pluggable
- **MUST** add unit tests in /tests/ directory

### When modifying shaders:
- **MUST** update both HLSL source and compilation scripts
- **MUST** verify cross-platform compatibility (DX12 and Vulkan)
- **MUST** update material system if new shader parameters added

## Build System Rules

### CMake Configuration
- **MUST** use CMake 3.20+ with modern target-based approach
- **MUST** support both Debug and Release configurations
- **MUST** use FetchContent for external dependencies
- **MUST** generate separate targets for engine core, editor, and plugins
- **MUST** configure platform-specific SDK paths (DirectX 12 Agility SDK, Vulkan SDK)

### Dependency Management
- **MUST** use vcpkg or Conan for C++ package management
- **NEVER** manually edit package configuration files
- **MUST** document all external dependencies in README.md
- **MUST** ensure all dependencies support both Windows and Linux

## AI Decision-Making Standards

### When implementing new features:
1. **Check guide.md** for existing architectural decisions
2. **Verify cross-platform compatibility** requirements
3. **Ensure modular design** - can it be implemented as a plugin?
4. **Consider performance impact** - does it fit data-oriented design?
5. **Check integration points** with existing subsystems

### Priority Order for Technical Decisions:
1. Cross-platform compatibility (Windows/Linux parity)
2. Performance optimization (data-oriented design)
3. Modular architecture (plugin system compatibility)
4. Code maintainability (clear interfaces)
5. Feature completeness

## Prohibited Actions

### **NEVER** do the following:
- Implement platform-specific code outside designated abstraction layers
- Use global variables or singletons (except for carefully designed system managers)
- Directly link against platform-specific libraries in core engine code
- Implement custom memory allocators without profiling justification
- Add dependencies that don't support both target platforms
- Modify core interfaces without updating all implementing classes
- Implement graphics features that can't work on both DirectX 12 and Vulkan
- Use blocking I/O operations on main thread
- Implement custom threading primitives instead of using job system

### **ALWAYS** do the following:
- Reference guide.md for architectural decisions
- Implement cross-platform abstractions for any OS-specific functionality
- Use the job system for any CPU-intensive operations
- Profile performance impact of new features
- Write unit tests for new subsystems
- Update documentation when adding new APIs
- Verify shader compilation for both graphics backends
- Use RAII and modern C++ best practices
