# Build directories
build/
bin/
obj/
out/
Debug/
Release/
x64/
x86/
.vs/
.vscode/settings.json
.vscode/launch.json

# Compiled Object files
*.o
*.obj
*.lo
*.slo

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# Visual Studio
*.vcxproj.user
*.vcxproj.filters
*.sln.docstates
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs

# Visual Studio Code
.vscode/
!.vscode/tasks.json
!.vscode/c_cpp_properties.json

# Temporary files
*.tmp
*.temp
*~
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Asset cache and generated files
*.cache
*.meta
*.asset
*.prefab
*.scene

# Logs
*.log
logs/

# Package managers
node_modules/
packages/
.nuget/

# IDE specific
*.cbp
*.layout
*.depend
*.workspace

# Profiling and debugging
*.pdb
*.idb
*.ilk
*.map
*.exp

# Game engine specific
Assets/StreamingAssets/
Library/
Temp/
ProjectSettings/
UserSettings/

# Backup files
*.bak
*.backup
*~

# Generated documentation
docs/html/
docs/latex/
