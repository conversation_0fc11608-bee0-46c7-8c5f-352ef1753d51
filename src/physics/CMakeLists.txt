# Physics Subsystem - Placeholder

add_library(EnginePhysics STATIC
    placeholder.cpp
)

target_include_directories(EnginePhysics
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EnginePhysics
    PUBLIC
        EngineCore
)

file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for physics subsystem\n"
    "namespace PlaytimeEngine { namespace Physics { void placeholder() {} } }\n"
)
