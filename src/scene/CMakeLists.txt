# Scene Management Subsystem - Placeholder

add_library(EngineScene STATIC
    placeholder.cpp
)

target_include_directories(EngineScene
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EngineScene
    PUBLIC
        EngineCore
        EnTT::EnTT
)

file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for scene subsystem\n"
    "namespace PlaytimeEngine { namespace Scene { void placeholder() {} } }\n"
)
