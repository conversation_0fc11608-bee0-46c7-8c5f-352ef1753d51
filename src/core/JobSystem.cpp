
/**
 * @file JobSystem.cpp
 * @brief High-performance job system implementation with work-stealing
 */

#include "JobSystem.h"
#include "../include/core/Platform.h"
#include <algorithm>
#include <chrono>
#include <cassert>
#include <iostream>

// Simple logging macros since Logging.h doesn't exist yet
#define LOG_INFO(category, format, ...) \
    std::cout << "[INFO][" << category << "] " << format << std::endl
#define LOG_WARNING(category, format, ...) \
    std::cout << "[WARNING][" << category << "] " << format << std::endl  
#define LOG_ERROR(category, format, ...) \
    std::cout << "[ERROR][" << category << "] " << format << std::endl

namespace PlaytimeEngine {

// Forward declare internal job wrapper  
struct InternalJob;

// Simple platform function implementations
namespace Platform {
    uint64 getCurrentTimeNanoseconds() {
        auto now = std::chrono::high_resolution_clock::now();
        return static_cast<uint64>(std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count());
    }
}

// Internal job wrapper for dependency tracking
struct InternalJob : public IJob {
    UniquePtr<IJob> job;
    std::shared_ptr<JobData> data;
    std::vector<JobHandle> dependencies;
    
    void execute() override {
        if (job) {
            data->state.store(JobState::Running);
            data->startTime = Platform::getCurrentTimeNanoseconds();
            
            try {
                job->execute();
                data->state.store(JobState::Complete);
            }
            catch (...) {
                data->state.store(JobState::Failed);
                throw;
            }
            
            data->endTime = Platform::getCurrentTimeNanoseconds();
        }
    }
    
    const char* getName() const override {
        return job ? job->getName() : "InternalJob";
    }
    
    int getPriority() const override {
        return job ? job->getPriority() : 0;
    }
    
    bool canBeStolen() const override {
        return job ? job->canBeStolen() : true;
    }
    
    uint64 getEstimatedTime() const override {
        return job ? job->getEstimatedTime() : 0;
    }
};

// Thread-local storage for worker index
thread_local uint32 JobSystem::currentWorkerIndex_ = UINT32_MAX;

// Global job system instance
static UniquePtr<JobSystem> g_jobSystem;
    : workerCount_(0)
    , shutdown_(false)
    , nextJobId_(1)
    , totalJobsExecuted_(0)
    , totalJobsStolen_(0)
    , totalExecutionTime_(0)
{
}

JobSystem::~JobSystem() {
    shutdown();
}

bool JobSystem::initialize(uint32 workerCount) {
    if (workerCount == 0) {
        workerCount = getOptimalWorkerCount();
    }
    
    LOG_INFO("JobSystem", "Initializing with {} worker threads", workerCount);
    
    workerCount_ = workerCount;
    workers_.reserve(workerCount_);
    
    // Create worker threads
    for (uint32 i = 0; i < workerCount_; ++i) {
        auto worker = std::make_unique<WorkerThread>();
        worker->index = i;
        worker->active.store(true);
        
        try {
            worker->thread = std::thread(&JobSystem::workerMain, this, i);
            workers_.push_back(std::move(worker));
        }
        catch (const std::exception& e) {
            LOG_ERROR("JobSystem", "Failed to create worker thread {}: {}", i, e.what());
            shutdown();
            return false;
        }
    }
    
    LOG_INFO("JobSystem", "JobSystem initialized successfully with {} workers", workerCount_);
    return true;
}

void JobSystem::shutdown() {
    if (shutdown_.load()) {
        return;
    }
    
    LOG_INFO("JobSystem", "Shutting down JobSystem...");
    
    shutdown_.store(true);
    
    // Signal all workers to stop
    for (auto& worker : workers_) {
        if (worker) {
            worker->active.store(false);
        }
    }
    
    // Wait for all workers to finish
    for (auto& worker : workers_) {
        if (worker && worker->thread.joinable()) {
            worker->thread.join();
        }
    }
    
    workers_.clear();
    workerCount_ = 0;
    
    LOG_INFO("JobSystem", "JobSystem shutdown complete");
}

JobHandle JobSystem::submit(UniquePtr<IJob> job) {
    return submit(std::move(job), {});
}

JobHandle JobSystem::submit(UniquePtr<IJob> job, const std::vector<JobHandle>& dependencies) {
    if (!job) {
        LOG_WARNING("JobSystem", "Attempted to submit null job");
        return JobHandle();
    }
    
    // Create job data
    uint32 jobId = nextJobId_.fetch_add(1);
    auto jobData = std::make_shared<JobData>(jobId);
    jobData->submitTime = Platform::getCurrentTimeNanoseconds();
    
    // Create internal job wrapper
    auto internalJob = std::make_unique<InternalJob>();
    internalJob->job = std::move(job);
    internalJob->data = jobData;
    internalJob->dependencies = dependencies;
    
    // Check if dependencies are satisfied
    bool canExecute = true;
    for (const auto& dep : dependencies) {
        if (dep.isValid() && !dep.isComplete()) {
            canExecute = false;
            break;
        }
    }
    
    if (canExecute) {
        // Submit to a worker queue
        uint32 targetWorker = jobId % workerCount_;
        if (targetWorker < workers_.size() && workers_[targetWorker]) {
            workers_[targetWorker]->jobQueue.push(internalJob.release());
        }
    } else {
        // Store in pending jobs (would need a pending jobs system for dependencies)
        LOG_WARNING("JobSystem", "Job dependencies not implemented yet");
        return JobHandle();
    }
    
    return JobHandle(jobData);
}

void JobSystem::wait(const JobHandle& handle) {
    if (!handle.isValid()) {
        return;
    }
    
    // Execute jobs on current thread while waiting
    executeUntil([&handle]() { return handle.isComplete(); });
}

void JobSystem::waitAll(const std::vector<JobHandle>& handles) {
    if (handles.empty()) {
        return;
    }
    
    executeUntil([&handles]() {
        return std::all_of(handles.begin(), handles.end(), 
                          [](const JobHandle& h) { return !h.isValid() || h.isComplete(); });
    });
}

bool JobSystem::isComplete(const JobHandle& handle) const {
    return !handle.isValid() || handle.isComplete();
}

void JobSystem::executeUntil(std::function<bool()> condition) {
    while (!condition() && !shutdown_.load()) {
        if (!executeOne()) {
            // No jobs available, yield thread
            std::this_thread::yield();
        }
    }
}

bool JobSystem::executeOne() {
    uint32 workerIndex = getCurrentWorkerIndex();
    IJob* job = getJob(workerIndex);
    
    if (job) {
        executeJob(job);
        return true;
    }
    
    return false;
}

uint32 JobSystem::getCurrentWorkerIndex() const {
    return currentWorkerIndex_;
}

bool JobSystem::isWorkerThread() const {
    return currentWorkerIndex_ != UINT32_MAX;
}

JobSystem::Stats JobSystem::getStats() const {
    Stats stats;
    stats.workerCount = workerCount_;
    stats.jobsExecuted = totalJobsExecuted_.load();
    stats.jobsStolen = totalJobsStolen_.load();
    stats.totalExecutionTime = totalExecutionTime_.load();
    
    if (stats.jobsExecuted > 0) {
        stats.averageJobTime = static_cast<float>(stats.totalExecutionTime) / stats.jobsExecuted;
    }
    
    // Count active and queued jobs
    for (const auto& worker : workers_) {
        if (worker) {
            stats.queuedJobs += static_cast<uint32>(worker->jobQueue.size());
        }
    }
    
    return stats;
}

void JobSystem::resetStats() {
    totalJobsExecuted_.store(0);
    totalJobsStolen_.store(0);
    totalExecutionTime_.store(0);
    
    for (auto& worker : workers_) {
        if (worker) {
            worker->jobsExecuted.store(0);
            worker->jobsStolen.store(0);
        }
    }
}

void JobSystem::setPriority(int priority) {
    // Platform-specific thread priority setting would go here
    LOG_INFO("JobSystem", "Setting thread priority to {}", priority);
}

void JobSystem::workerMain(uint32 workerIndex) {
    currentWorkerIndex_ = workerIndex;
    
    LOG_INFO("JobSystem", "Worker thread {} started", workerIndex);
    
    while (!shutdown_.load() && workerIndex < workers_.size() && 
           workers_[workerIndex] && workers_[workerIndex]->active.load()) {
        
        IJob* job = getJob(workerIndex);
        if (job) {
            executeJob(job);
            workers_[workerIndex]->jobsExecuted.fetch_add(1);
        } else {
            // No jobs available, yield thread
            std::this_thread::yield();
        }
    }
    
    LOG_INFO("JobSystem", "Worker thread {} stopped", workerIndex);
    currentWorkerIndex_ = UINT32_MAX;
}

IJob* JobSystem::getJob(uint32 workerIndex) {
    // Try to get job from own queue first
    if (workerIndex < workers_.size() && workers_[workerIndex]) {
        IJob* job = workers_[workerIndex]->jobQueue.pop();
        if (job) {
            return job;
        }
    }
    
    // Try to steal from other workers
    return stealJob(workerIndex);
}

IJob* JobSystem::stealJob(uint32 excludeWorker) {
    // Try to steal from other workers in random order
    uint32 startIndex = Platform::getCurrentTimeNanoseconds() % workerCount_;
    
    for (uint32 i = 0; i < workerCount_; ++i) {
        uint32 targetIndex = (startIndex + i) % workerCount_;
        
        if (targetIndex == excludeWorker || targetIndex >= workers_.size() || !workers_[targetIndex]) {
            continue;
        }
        
        IJob* job = workers_[targetIndex]->jobQueue.steal();
        if (job) {
            // Update steal statistics
            if (excludeWorker < workers_.size() && workers_[excludeWorker]) {
                workers_[excludeWorker]->jobsStolen.fetch_add(1);
                totalJobsStolen_.fetch_add(1);
            }
            return job;
        }
    }
    
    return nullptr;
}

void JobSystem::executeJob(IJob* job) {
    if (!job) {
        return;
    }
    
    uint64 startTime = Platform::getCurrentTimeNanoseconds();
    
    try {
        job->execute();
        totalJobsExecuted_.fetch_add(1);
    }
    catch (const std::exception& e) {
        LOG_ERROR("JobSystem", "Job '{}' threw exception: {}", job->getName(), e.what());
    }
    catch (...) {
        LOG_ERROR("JobSystem", "Job '{}' threw unknown exception", job->getName());
    }
    
    uint64 endTime = Platform::getCurrentTimeNanoseconds();
    uint64 executionTime = endTime - startTime;
    totalExecutionTime_.fetch_add(executionTime);
    
    // Clean up job
    delete job;
}

uint32 JobSystem::getOptimalWorkerCount() const {
    uint32 hardwareThreads = std::thread::hardware_concurrency();
    
    if (hardwareThreads == 0) {
        LOG_WARNING("JobSystem", "Could not detect hardware thread count, using 4");
        return 4;
    }
    
    // Use all available threads minus one for main thread
    return std::max(1u, hardwareThreads - 1);
}

// Global job system functions
namespace Jobs {
    
    bool initialize(uint32 workerCount) {
        if (g_jobSystem) {
            LOG_WARNING("JobSystem", "Global job system already initialized");
            return true;
        }
        
        g_jobSystem = std::make_unique<JobSystem>();
        return g_jobSystem->initialize(workerCount);
    }
    
    void shutdown() {
        if (g_jobSystem) {
            g_jobSystem->shutdown();
            g_jobSystem.reset();
        }
    }
    
    JobSystem* getSystem() {
        return g_jobSystem.get();
    }
    
    JobHandle submit(UniquePtr<IJob> job) {
        if (g_jobSystem) {
            return g_jobSystem->submit(std::move(job));
        }
        LOG_ERROR("JobSystem", "Global job system not initialized");
        return JobHandle();
    }
    
    void wait(const JobHandle& handle) {
        if (g_jobSystem) {
            g_jobSystem->wait(handle);
        }
    }
    
    void waitAll(const std::vector<JobHandle>& handles) {
        if (g_jobSystem) {
            g_jobSystem->waitAll(handles);
        }
    }
    
    bool isComplete(const JobHandle& handle) {
        if (g_jobSystem) {
            return g_jobSystem->isComplete(handle);
        }
        return true;
    }

} // namespace Jobs

} // namespace PlaytimeEngine
