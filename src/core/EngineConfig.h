#pragma once

/**
 * @file EngineConfig.h
 * @brief Engine configuration structure
 */

#include "Types.h"

namespace PlaytimeEngine {

/**
 * @brief Rendering configuration
 */
struct RenderConfig {
    uint32 windowWidth = 1920;
    uint32 windowHeight = 1080;
    bool fullscreen = false;
    bool vsync = true;
    uint32 maxFrameRate = 60;
    String windowTitle = "Project Playtime Engine";
};

/**
 * @brief Audio configuration
 */
struct AudioConfig {
    uint32 sampleRate = 44100;
    uint32 bufferSize = 1024;
    uint32 channels = 2;
    float masterVolume = 1.0f;
};

/**
 * @brief Physics configuration
 */
struct PhysicsConfig {
    float gravity = -9.81f;
    uint32 maxSubSteps = 8;
    float fixedTimeStep = 1.0f / 60.0f;
    bool enableCCD = true;
};

/**
 * @brief Main engine configuration
 */
struct ENGINE_API EngineConfig {
    String configFile;
    String logFile = "engine.log";
    uint32 logLevel = 3; // Info level
    bool debugMode = false;
    
    RenderConfig renderConfig;
    AudioConfig audioConfig;
    PhysicsConfig physicsConfig;
    
    // Job system configuration
    uint32 workerThreadCount = 0; // 0 = auto-detect
    
    // Memory configuration
    size_t heapSize = 256 * MEGABYTE;
    size_t stackSize = 16 * MEGABYTE;
    
    /**
     * @brief Load configuration from file
     */
    bool loadFromFile(const String& filename);
    
    /**
     * @brief Save configuration to file
     */
    bool saveToFile(const String& filename) const;
    
    /**
     * @brief Get default configuration
     */
    static EngineConfig getDefault();
};

} // namespace PlaytimeEngine
