#pragma once

/**
 * @file Hash.h
 * @brief Hash functions and utilities
 */

#include "Types.h"

namespace PlaytimeEngine {

/**
 * @brief FNV-1a hash function for 64-bit
 */
constexpr Hash FNV_OFFSET_BASIS_64 = 14695981039346656037ULL;
constexpr Hash FNV_PRIME_64 = 1099511628211ULL;

/**
 * @brief Compile-time FNV-1a hash
 */
constexpr Hash hashFNV1a(const char* str, Hash hash = FNV_OFFSET_BASIS_64) {
    return (*str == '\0') ? hash : hashFNV1a(str + 1, (hash ^ *str) * FNV_PRIME_64);
}

/**
 * @brief Runtime FNV-1a hash
 */
ENGINE_API Hash hashFNV1a(const void* data, size_t size, Hash hash = FNV_OFFSET_BASIS_64);

/**
 * @brief Hash a string
 */
ENGINE_API Hash hashString(const char* str);
ENGINE_API Hash hashString(const String& str);

/**
 * @brief Hash arbitrary data
 */
template<typename T>
Hash hashValue(const T& value) {
    return hashFNV1a(&value, sizeof(T));
}

/**
 * @brief Combine two hash values
 */
constexpr Hash hashCombine(Hash a, Hash b) {
    return a ^ (b + 0x9e3779b9 + (a << 6) + (a >> 2));
}

/**
 * @brief Hash multiple values
 */
template<typename T, typename... Args>
Hash hashMultiple(const T& first, const Args&... args) {
    if constexpr (sizeof...(args) == 0) {
        return hashValue(first);
    } else {
        return hashCombine(hashValue(first), hashMultiple(args...));
    }
}

/**
 * @brief String literal hash operator
 */
constexpr Hash operator""_hash(const char* str, size_t) {
    return hashFNV1a(str);
}

/**
 * @brief Hash struct for use with std::unordered_map
 */
template<typename T>
struct Hasher {
    Hash operator()(const T& value) const {
        return hashValue(value);
    }
};

// Specialization for strings
template<>
struct Hasher<String> {
    Hash operator()(const String& str) const {
        return hashString(str);
    }
};

} // namespace PlaytimeEngine
