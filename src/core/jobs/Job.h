#pragma once

/**
 * @file Job.h
 * @brief Base job interface and common job types
 */

#include "../Types.h"
#include <functional>
#include <vector>

namespace PlaytimeEngine {

// Forward declarations
class JobHandle;

/**
 * @brief Base interface for all jobs
 */
class ENGINE_API IJob : public NonCopyable {
public:
    virtual ~IJob() = default;
    
    /**
     * @brief Execute the job
     */
    virtual void execute() = 0;
    
    /**
     * @brief Get job name for debugging
     */
    virtual const char* getName() const { return "IJob"; }
    
    /**
     * @brief Get job priority (higher = more important)
     */
    virtual int getPriority() const { return 0; }
    
    /**
     * @brief Check if job can be stolen by other threads
     */
    virtual bool canBeStolen() const { return true; }
    
    /**
     * @brief Get estimated execution time in microseconds
     */
    virtual uint64 getEstimatedTime() const { return 0; }
};

/**
 * @brief Lambda job wrapper
 */
template<typename Func>
class LambdaJob : public IJob {
public:
    explicit LambdaJob(Func&& func) : func_(std::forward<Func>(func)) {}
    
    void execute() override {
        func_();
    }
    
    const char* getName() const override { return "LambdaJob"; }

private:
    Func func_;
};

/**
 * @brief Function pointer job
 */
class ENGINE_API FunctionJob : public IJob {
public:
    using FunctionPtr = void(*)(void*);
    
    FunctionJob(FunctionPtr func, void* userData = nullptr)
        : func_(func), userData_(userData) {}
    
    void execute() override {
        if (func_) {
            func_(userData_);
        }
    }
    
    const char* getName() const override { return "FunctionJob"; }

private:
    FunctionPtr func_;
    void* userData_;
};

/**
 * @brief Job that executes multiple child jobs
 */
class ENGINE_API CompositeJob : public IJob {
public:
    CompositeJob() = default;
    
    /**
     * @brief Add a child job
     */
    void addJob(UniquePtr<IJob> job) {
        jobs_.push_back(std::move(job));
    }
    
    /**
     * @brief Add multiple child jobs
     */
    void addJobs(std::vector<UniquePtr<IJob>> jobs) {
        for (auto& job : jobs) {
            jobs_.push_back(std::move(job));
        }
    }
    
    void execute() override {
        for (auto& job : jobs_) {
            job->execute();
        }
    }
    
    const char* getName() const override { return "CompositeJob"; }
    
    /**
     * @brief Get number of child jobs
     */
    size_t getJobCount() const { return jobs_.size(); }

private:
    std::vector<UniquePtr<IJob>> jobs_;
};

/**
 * @brief Job with continuation
 */
class ENGINE_API ContinuationJob : public IJob {
public:
    ContinuationJob(UniquePtr<IJob> job, std::function<void()> continuation)
        : job_(std::move(job)), continuation_(continuation) {}
    
    void execute() override {
        job_->execute();
        if (continuation_) {
            continuation_();
        }
    }
    
    const char* getName() const override { return "ContinuationJob"; }

private:
    UniquePtr<IJob> job_;
    std::function<void()> continuation_;
};

/**
 * @brief Job that waits for dependencies before executing
 */
class ENGINE_API DependentJob : public IJob {
public:
    DependentJob(UniquePtr<IJob> job, std::vector<JobHandle> dependencies);
    
    void execute() override;
    
    const char* getName() const override { return "DependentJob"; }
    
    /**
     * @brief Check if all dependencies are complete
     */
    bool areDependenciesComplete() const;

private:
    UniquePtr<IJob> job_;
    std::vector<JobHandle> dependencies_;
};

/**
 * @brief Job that executes on a specific thread
 */
class ENGINE_API AffinityJob : public IJob {
public:
    AffinityJob(UniquePtr<IJob> job, uint32 threadIndex)
        : job_(std::move(job)), threadIndex_(threadIndex) {}
    
    void execute() override {
        job_->execute();
    }
    
    const char* getName() const override { return "AffinityJob"; }
    
    bool canBeStolen() const override { return false; }
    
    /**
     * @brief Get preferred thread index
     */
    uint32 getThreadIndex() const { return threadIndex_; }

private:
    UniquePtr<IJob> job_;
    uint32 threadIndex_;
};

/**
 * @brief Job with timeout
 */
class ENGINE_API TimeoutJob : public IJob {
public:
    TimeoutJob(UniquePtr<IJob> job, uint64 timeoutMicroseconds)
        : job_(std::move(job)), timeoutMicroseconds_(timeoutMicroseconds) {}
    
    void execute() override;
    
    const char* getName() const override { return "TimeoutJob"; }
    
    /**
     * @brief Check if job timed out
     */
    bool hasTimedOut() const { return timedOut_; }

private:
    UniquePtr<IJob> job_;
    uint64 timeoutMicroseconds_;
    bool timedOut_ = false;
};

/**
 * @brief Job factory functions
 */
namespace JobFactory {
    /**
     * @brief Create a lambda job
     */
    template<typename Func>
    UniquePtr<IJob> createLambda(Func&& func) {
        return std::make_unique<LambdaJob<Func>>(std::forward<Func>(func));
    }
    
    /**
     * @brief Create a function job
     */
    ENGINE_API UniquePtr<IJob> createFunction(FunctionJob::FunctionPtr func, void* userData = nullptr);
    
    /**
     * @brief Create a composite job
     */
    ENGINE_API UniquePtr<CompositeJob> createComposite();
    
    /**
     * @brief Create a continuation job
     */
    ENGINE_API UniquePtr<IJob> createContinuation(UniquePtr<IJob> job, std::function<void()> continuation);
    
    /**
     * @brief Create a dependent job
     */
    ENGINE_API UniquePtr<IJob> createDependent(UniquePtr<IJob> job, std::vector<JobHandle> dependencies);
    
    /**
     * @brief Create an affinity job
     */
    ENGINE_API UniquePtr<IJob> createAffinity(UniquePtr<IJob> job, uint32 threadIndex);
    
    /**
     * @brief Create a timeout job
     */
    ENGINE_API UniquePtr<IJob> createTimeout(UniquePtr<IJob> job, uint64 timeoutMicroseconds);
}

} // namespace PlaytimeEngine
