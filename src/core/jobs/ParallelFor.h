#pragma once

/**
 * @file ParallelFor.h
 * @brief Parallel-for job implementation
 */

#include "Job.h"
#include "../JobSystem.h"
#include <vector>

namespace PlaytimeEngine {

/**
 * @brief Parallel-for job that splits work across multiple threads
 */
template<typename Func>
class ParallelForJob : public IJob {
public:
    /**
     * @brief Constructor
     * @param start Start index (inclusive)
     * @param end End index (exclusive)
     * @param func Function to execute for each index
     * @param batchSize Number of items per batch
     */
    ParallelForJob(uint32 start, uint32 end, Func func, uint32 batchSize = 1)
        : start_(start), end_(end), func_(func), batchSize_(batchSize) {
        
        // Ensure we have at least one item per batch
        if (batchSize_ == 0) batchSize_ = 1;
        
        // Calculate number of batches
        uint32 totalItems = end_ - start_;
        batchCount_ = (totalItems + batchSize_ - 1) / batchSize_;
    }
    
    void execute() override {
        // If we have a small number of items, execute sequentially
        if (batchCount_ <= 1 || (end_ - start_) <= batchSize_) {
            executeSequential();
            return;
        }
        
        // Execute in parallel
        executeParallel();
    }
    
    const char* getName() const override { return "ParallelForJob"; }
    
    uint64 getEstimatedTime() const override {
        // Estimate based on number of items
        return (end_ - start_) * 10; // 10 microseconds per item (rough estimate)
    }

private:
    /**
     * @brief Execute sequentially
     */
    void executeSequential() {
        for (uint32 i = start_; i < end_; ++i) {
            func_(i);
        }
    }
    
    /**
     * @brief Execute in parallel
     */
    void executeParallel() {
        // Create batch jobs
        std::vector<JobHandle> batchJobs;
        batchJobs.reserve(batchCount_);
        
        for (uint32 batch = 0; batch < batchCount_; ++batch) {
            uint32 batchStart = start_ + batch * batchSize_;
            uint32 batchEnd = std::min(batchStart + batchSize_, end_);
            
            auto batchJob = std::make_unique<BatchJob>(batchStart, batchEnd, func_);
            
            // Submit to job system if available, otherwise execute directly
            if (auto* jobSystem = Jobs::getSystem()) {
                batchJobs.push_back(jobSystem->submit(std::move(batchJob)));
            } else {
                batchJob->execute();
            }
        }
        
        // Wait for all batch jobs to complete
        if (!batchJobs.empty()) {
            Jobs::waitAll(batchJobs);
        }
    }
    
    /**
     * @brief Individual batch job
     */
    class BatchJob : public IJob {
    public:
        BatchJob(uint32 start, uint32 end, const Func& func)
            : start_(start), end_(end), func_(func) {}
        
        void execute() override {
            for (uint32 i = start_; i < end_; ++i) {
                func_(i);
            }
        }
        
        const char* getName() const override { return "ParallelForBatch"; }
        
    private:
        uint32 start_;
        uint32 end_;
        Func func_;
    };

private:
    uint32 start_;
    uint32 end_;
    Func func_;
    uint32 batchSize_;
    uint32 batchCount_;
};

/**
 * @brief Parallel-for with range-based iteration
 */
template<typename Iterator, typename Func>
class ParallelForRangeJob : public IJob {
public:
    ParallelForRangeJob(Iterator begin, Iterator end, Func func, size_t batchSize = 1)
        : begin_(begin), end_(end), func_(func), batchSize_(batchSize) {
        
        if (batchSize_ == 0) batchSize_ = 1;
        
        size_t totalItems = std::distance(begin_, end_);
        batchCount_ = (totalItems + batchSize_ - 1) / batchSize_;
    }
    
    void execute() override {
        if (batchCount_ <= 1 || std::distance(begin_, end_) <= static_cast<ptrdiff_t>(batchSize_)) {
            executeSequential();
            return;
        }
        
        executeParallel();
    }
    
    const char* getName() const override { return "ParallelForRangeJob"; }

private:
    void executeSequential() {
        for (auto it = begin_; it != end_; ++it) {
            func_(*it);
        }
    }
    
    void executeParallel() {
        std::vector<JobHandle> batchJobs;
        batchJobs.reserve(batchCount_);
        
        auto current = begin_;
        for (size_t batch = 0; batch < batchCount_ && current != end_; ++batch) {
            auto batchEnd = current;
            std::advance(batchEnd, std::min(batchSize_, static_cast<size_t>(std::distance(current, end_))));
            
            auto batchJob = std::make_unique<RangeBatchJob>(current, batchEnd, func_);
            
            if (auto* jobSystem = Jobs::getSystem()) {
                batchJobs.push_back(jobSystem->submit(std::move(batchJob)));
            } else {
                batchJob->execute();
            }
            
            current = batchEnd;
        }
        
        if (!batchJobs.empty()) {
            Jobs::waitAll(batchJobs);
        }
    }
    
    class RangeBatchJob : public IJob {
    public:
        RangeBatchJob(Iterator begin, Iterator end, const Func& func)
            : begin_(begin), end_(end), func_(func) {}
        
        void execute() override {
            for (auto it = begin_; it != end_; ++it) {
                func_(*it);
            }
        }
        
        const char* getName() const override { return "ParallelForRangeBatch"; }
        
    private:
        Iterator begin_;
        Iterator end_;
        Func func_;
    };

private:
    Iterator begin_;
    Iterator end_;
    Func func_;
    size_t batchSize_;
    size_t batchCount_;
};

/**
 * @brief Parallel-for utility functions
 */
namespace ParallelFor {
    /**
     * @brief Execute parallel-for with index-based iteration
     */
    template<typename Func>
    JobHandle execute(uint32 start, uint32 end, Func&& func, uint32 batchSize = 0) {
        // Auto-calculate batch size if not specified
        if (batchSize == 0) {
            uint32 totalItems = end - start;
            uint32 workerCount = Jobs::getSystem() ? Jobs::getSystem()->getWorkerCount() : 1;
            batchSize = std::max(1u, totalItems / (workerCount * 4)); // 4 batches per worker
        }
        
        auto job = std::make_unique<ParallelForJob<Func>>(start, end, std::forward<Func>(func), batchSize);
        return Jobs::submit(std::move(job));
    }
    
    /**
     * @brief Execute parallel-for with range-based iteration
     */
    template<typename Iterator, typename Func>
    JobHandle executeRange(Iterator begin, Iterator end, Func&& func, size_t batchSize = 0) {
        if (batchSize == 0) {
            size_t totalItems = std::distance(begin, end);
            uint32 workerCount = Jobs::getSystem() ? Jobs::getSystem()->getWorkerCount() : 1;
            batchSize = std::max(1ul, totalItems / (workerCount * 4));
        }
        
        auto job = std::make_unique<ParallelForRangeJob<Iterator, Func>>(begin, end, std::forward<Func>(func), batchSize);
        return Jobs::submit(std::move(job));
    }
    
    /**
     * @brief Execute parallel-for on a container
     */
    template<typename Container, typename Func>
    JobHandle executeContainer(Container& container, Func&& func, size_t batchSize = 0) {
        return executeRange(container.begin(), container.end(), std::forward<Func>(func), batchSize);
    }
}

} // namespace PlaytimeEngine
