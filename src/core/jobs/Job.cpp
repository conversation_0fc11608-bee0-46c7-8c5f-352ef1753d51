
/**
 * @file Job.cpp
 * @brief Base job interface implementation
 */

#include "../../include/core/jobs/Job.h"

namespace PlaytimeEngine {

// Base implementation already in header (pure virtual)
// Non-template job implementations would go here

FunctionJob::FunctionJob(FunctionPtr func, void* userData)
    : func_(func), userData_(userData) {
}

void FunctionJob::execute() {
    if (func_) {
        func_(userData_);
    }
}

const char* FunctionJob::getName() const {
    return "FunctionJob";
}

// CompositeJob implementation
CompositeJob::CompositeJob() = default;

void CompositeJob::addJob(UniquePtr<IJob> job) {
    if (job) {
        jobs_.push_back(std::move(job));
    }
}

void CompositeJob::execute() {
    for (auto& job : jobs_) {
        if (job) {
            job->execute();
        }
    }
}

const char* CompositeJob::getName() const {
    return "CompositeJob";
}

} // namespace PlaytimeEngine
