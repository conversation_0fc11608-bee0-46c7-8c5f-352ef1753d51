#pragma once

/**
 * @file JobHandle.h
 * @brief Job handle for tracking job completion
 */

#include "../Types.h"
#include <atomic>
#include <memory>

namespace PlaytimeEngine {

/**
 * @brief Job completion state
 */
enum class JobState : uint32 {
    Pending,    ///< Job is waiting to be executed
    Running,    ///< Job is currently executing
    Complete,   ///< Job has completed successfully
    Failed,     ///< Job failed during execution
    Cancelled   ///< Job was cancelled before execution
};

/**
 * @brief Internal job data shared between handle and job system
 */
struct JobData {
    std::atomic<JobState> state{JobState::Pending};
    std::atomic<uint32> refCount{1};
    uint32 jobId;
    uint64 submitTime;
    uint64 startTime;
    uint64 endTime;
    
    JobData(uint32 id) : jobId(id), submitTime(0), startTime(0), endTime(0) {}
};

/**
 * @brief Handle for tracking job completion and status
 */
class ENGINE_API JobHandle {
public:
    /**
     * @brief Default constructor (invalid handle)
     */
    JobHandle() : data_(nullptr) {}
    
    /**
     * @brief Constructor with job data
     */
    explicit JobHandle(std::shared_ptr<JobData> data) : data_(data) {}
    
    /**
     * @brief Copy constructor
     */
    JobHandle(const JobHandle& other) : data_(other.data_) {}
    
    /**
     * @brief Move constructor
     */
    JobHandle(JobHandle&& other) noexcept : data_(std::move(other.data_)) {}
    
    /**
     * @brief Copy assignment
     */
    JobHandle& operator=(const JobHandle& other) {
        data_ = other.data_;
        return *this;
    }
    
    /**
     * @brief Move assignment
     */
    JobHandle& operator=(JobHandle&& other) noexcept {
        data_ = std::move(other.data_);
        return *this;
    }
    
    /**
     * @brief Check if handle is valid
     */
    bool isValid() const { return data_ != nullptr; }
    
    /**
     * @brief Check if job is complete
     */
    bool isComplete() const {
        return data_ && (data_->state.load() == JobState::Complete || 
                        data_->state.load() == JobState::Failed ||
                        data_->state.load() == JobState::Cancelled);
    }
    
    /**
     * @brief Check if job completed successfully
     */
    bool isSuccessful() const {
        return data_ && data_->state.load() == JobState::Complete;
    }
    
    /**
     * @brief Check if job failed
     */
    bool isFailed() const {
        return data_ && data_->state.load() == JobState::Failed;
    }
    
    /**
     * @brief Check if job was cancelled
     */
    bool isCancelled() const {
        return data_ && data_->state.load() == JobState::Cancelled;
    }
    
    /**
     * @brief Check if job is currently running
     */
    bool isRunning() const {
        return data_ && data_->state.load() == JobState::Running;
    }
    
    /**
     * @brief Check if job is pending
     */
    bool isPending() const {
        return data_ && data_->state.load() == JobState::Pending;
    }
    
    /**
     * @brief Get job state
     */
    JobState getState() const {
        return data_ ? data_->state.load() : JobState::Failed;
    }
    
    /**
     * @brief Get job ID
     */
    uint32 getJobId() const {
        return data_ ? data_->jobId : 0;
    }
    
    /**
     * @brief Get submit time
     */
    uint64 getSubmitTime() const {
        return data_ ? data_->submitTime : 0;
    }
    
    /**
     * @brief Get start time
     */
    uint64 getStartTime() const {
        return data_ ? data_->startTime : 0;
    }
    
    /**
     * @brief Get end time
     */
    uint64 getEndTime() const {
        return data_ ? data_->endTime : 0;
    }
    
    /**
     * @brief Get execution time in microseconds
     */
    uint64 getExecutionTime() const {
        if (!data_ || data_->startTime == 0) return 0;
        uint64 endTime = data_->endTime != 0 ? data_->endTime : getCurrentTime();
        return endTime - data_->startTime;
    }
    
    /**
     * @brief Get wait time in microseconds (submit to start)
     */
    uint64 getWaitTime() const {
        if (!data_ || data_->submitTime == 0 || data_->startTime == 0) return 0;
        return data_->startTime - data_->submitTime;
    }
    
    /**
     * @brief Get total time in microseconds (submit to end)
     */
    uint64 getTotalTime() const {
        if (!data_ || data_->submitTime == 0) return 0;
        uint64 endTime = data_->endTime != 0 ? data_->endTime : getCurrentTime();
        return endTime - data_->submitTime;
    }
    
    /**
     * @brief Comparison operators
     */
    bool operator==(const JobHandle& other) const {
        return data_ == other.data_;
    }
    
    bool operator!=(const JobHandle& other) const {
        return data_ != other.data_;
    }
    
    bool operator<(const JobHandle& other) const {
        if (!data_ && !other.data_) return false;
        if (!data_) return true;
        if (!other.data_) return false;
        return data_->jobId < other.data_->jobId;
    }
    
    /**
     * @brief Hash function for use in containers
     */
    size_t hash() const {
        return data_ ? std::hash<uint32>{}(data_->jobId) : 0;
    }

private:
    friend class JobSystem;
    
    /**
     * @brief Get current time for timing calculations
     */
    uint64 getCurrentTime() const;
    
    /**
     * @brief Set job state (internal use only)
     */
    void setState(JobState state) {
        if (data_) {
            data_->state.store(state);
        }
    }
    
    /**
     * @brief Set timing information (internal use only)
     */
    void setSubmitTime(uint64 time) {
        if (data_) data_->submitTime = time;
    }
    
    void setStartTime(uint64 time) {
        if (data_) data_->startTime = time;
    }
    
    void setEndTime(uint64 time) {
        if (data_) data_->endTime = time;
    }

private:
    std::shared_ptr<JobData> data_;
};

/**
 * @brief Invalid job handle constant
 */
extern ENGINE_API const JobHandle INVALID_JOB_HANDLE;

} // namespace PlaytimeEngine

/**
 * @brief Hash specialization for JobHandle
 */
namespace std {
    template<>
    struct hash<PlaytimeEngine::JobHandle> {
        size_t operator()(const PlaytimeEngine::JobHandle& handle) const {
            return handle.hash();
        }
    };
}

/**
 * @brief Job handle utility functions
 */
namespace PlaytimeEngine {
namespace JobHandleUtils {
    /**
     * @brief Create a completed job handle
     */
    ENGINE_API JobHandle createCompleted();
    
    /**
     * @brief Create a failed job handle
     */
    ENGINE_API JobHandle createFailed();
    
    /**
     * @brief Create a cancelled job handle
     */
    ENGINE_API JobHandle createCancelled();
    
    /**
     * @brief Wait for all handles to complete
     */
    ENGINE_API void waitForAll(const std::vector<JobHandle>& handles);
    
    /**
     * @brief Wait for any handle to complete
     */
    ENGINE_API JobHandle waitForAny(const std::vector<JobHandle>& handles);
    
    /**
     * @brief Check if all handles are complete
     */
    ENGINE_API bool areAllComplete(const std::vector<JobHandle>& handles);
    
    /**
     * @brief Check if any handle is complete
     */
    ENGINE_API bool isAnyComplete(const std::vector<JobHandle>& handles);
}
}
