
/**
 * @file JobHandle.cpp
 * @brief Job handle implementation
 */

#include "../../include/core/jobs/JobHandle.h"

namespace PlaytimeEngine {

// Implementation is mostly in the header due to inline methods
// Additional non-inline methods would go here if needed

JobData::JobData(uint32 id) 
    : state(JobState::Pending)
    , refCount(1)
    , jobId(id)
    , submitTime(0)
    , startTime(0)
    , endTime(0) {
}

} // namespace PlaytimeEngine
