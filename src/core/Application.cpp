/**
 * @file Application.cpp
 * @brief Base application implementation
 */

#include "Application.h"
#include "Engine.h"
#include "Logger.h"

namespace PlaytimeEngine {

Application::Application() 
    : engine_(nullptr)
    , exitRequested_(false)
    , initialized_(false) {
}

Application::~Application() {
    if (initialized_) {
        shutdown();
    }
}

EngineConfig Application::getEngineConfig() const {
    return EngineConfig::getDefault();
}

int Application::run() {
    LOG_INFO("Starting application: %s", getName());
    
    // Create engine
    engine_ = Engine::getInstance();
    if (!engine_) {
        auto engineInstance = std::make_unique<Engine>();
        engine_ = engineInstance.get();
        
        // Initialize engine
        EngineConfig config = getEngineConfig();
        auto result = engine_->initialize(config);
        if (result != Engine::InitResult::Success) {
            LOG_ERROR("Failed to initialize engine");
            return -1;
        }
        
        engineInstance.release(); // Engine manages its own lifetime
    }
    
    // Initialize application
    if (!initialize()) {
        LOG_ERROR("Failed to initialize application");
        return -1;
    }
    
    initialized_ = true;
    
    // Run engine
    int exitCode = engine_->run();
    
    // Cleanup
    shutdown();
    
    LOG_INFO("Application finished with exit code: %d", exitCode);
    return exitCode;
}

} // namespace PlaytimeEngine
