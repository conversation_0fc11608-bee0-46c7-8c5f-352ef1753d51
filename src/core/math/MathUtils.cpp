/**
 * @file MathUtils.cpp
 * @brief Math utilities implementation
 */

#include "MathUtils.h"
#include <random>
#include <ctime>

namespace PlaytimeEngine {
namespace Math {

float fastInverseSqrt(float x) {
    // Quake's fast inverse square root
    union {
        float f;
        uint32_t i;
    } conv = { .f = x };
    conv.i = 0x5f3759df - (conv.i >> 1);
    conv.f *= 1.5f - (x * 0.5f * conv.f * conv.f);
    return conv.f;
}

float wrapAngle(float angle) {
    while (angle > PI) angle -= TWO_PI;
    while (angle < -PI) angle += TWO_PI;
    return angle;
}

float wrapAngle2Pi(float angle) {
    while (angle >= TWO_PI) angle -= TWO_PI;
    while (angle < 0.0f) angle += TWO_PI;
    return angle;
}

uint32 nextPowerOfTwo(uint32 value) {
    if (value == 0) return 1;
    value--;
    value |= value >> 1;
    value |= value >> 2;
    value |= value >> 4;
    value |= value >> 8;
    value |= value >> 16;
    return value + 1;
}

uint64 nextPowerOfTwo(uint64 value) {
    if (value == 0) return 1;
    value--;
    value |= value >> 1;
    value |= value >> 2;
    value |= value >> 4;
    value |= value >> 8;
    value |= value >> 16;
    value |= value >> 32;
    return value + 1;
}

uint32 prevPowerOfTwo(uint32 value) {
    if (value == 0) return 0;
    value |= value >> 1;
    value |= value >> 2;
    value |= value >> 4;
    value |= value >> 8;
    value |= value >> 16;
    return value - (value >> 1);
}

uint64 prevPowerOfTwo(uint64 value) {
    if (value == 0) return 0;
    value |= value >> 1;
    value |= value >> 2;
    value |= value >> 4;
    value |= value >> 8;
    value |= value >> 16;
    value |= value >> 32;
    return value - (value >> 1);
}

uint32 countLeadingZeros(uint32 value) {
    if (value == 0) return 32;
    uint32 count = 0;
    if ((value & 0xFFFF0000) == 0) { count += 16; value <<= 16; }
    if ((value & 0xFF000000) == 0) { count += 8; value <<= 8; }
    if ((value & 0xF0000000) == 0) { count += 4; value <<= 4; }
    if ((value & 0xC0000000) == 0) { count += 2; value <<= 2; }
    if ((value & 0x80000000) == 0) { count += 1; }
    return count;
}

uint32 countLeadingZeros(uint64 value) {
    if (value == 0) return 64;
    uint32 count = 0;
    if ((value & 0xFFFFFFFF00000000ULL) == 0) { count += 32; value <<= 32; }
    return count + countLeadingZeros(static_cast<uint32>(value >> 32));
}

uint32 countTrailingZeros(uint32 value) {
    if (value == 0) return 32;
    uint32 count = 0;
    if ((value & 0x0000FFFF) == 0) { count += 16; value >>= 16; }
    if ((value & 0x000000FF) == 0) { count += 8; value >>= 8; }
    if ((value & 0x0000000F) == 0) { count += 4; value >>= 4; }
    if ((value & 0x00000003) == 0) { count += 2; value >>= 2; }
    if ((value & 0x00000001) == 0) { count += 1; }
    return count;
}

uint32 countTrailingZeros(uint64 value) {
    if (value == 0) return 64;
    uint32 count = 0;
    if ((value & 0x00000000FFFFFFFFULL) == 0) { count += 32; value >>= 32; }
    return count + countTrailingZeros(static_cast<uint32>(value));
}

uint32 popCount(uint32 value) {
    value = value - ((value >> 1) & 0x55555555);
    value = (value & 0x33333333) + ((value >> 2) & 0x33333333);
    return (((value + (value >> 4)) & 0x0F0F0F0F) * 0x01010101) >> 24;
}

uint32 popCount(uint64 value) {
    return popCount(static_cast<uint32>(value)) + popCount(static_cast<uint32>(value >> 32));
}

uint32 bitReverse(uint32 value) {
    value = ((value & 0xAAAAAAAA) >> 1) | ((value & 0x55555555) << 1);
    value = ((value & 0xCCCCCCCC) >> 2) | ((value & 0x33333333) << 2);
    value = ((value & 0xF0F0F0F0) >> 4) | ((value & 0x0F0F0F0F) << 4);
    value = ((value & 0xFF00FF00) >> 8) | ((value & 0x00FF00FF) << 8);
    return (value >> 16) | (value << 16);
}

uint64 bitReverse(uint64 value) {
    uint32 high = bitReverse(static_cast<uint32>(value));
    uint32 low = bitReverse(static_cast<uint32>(value >> 32));
    return (static_cast<uint64>(high) << 32) | low;
}

namespace Random {
    static std::mt19937 generator;
    static std::uniform_real_distribution<float> floatDist(0.0f, 1.0f);
    static std::uniform_int_distribution<int> boolDist(0, 1);

    void setSeed(uint32 seed) {
        generator.seed(seed);
    }

    float randomFloat() {
        return floatDist(generator);
    }

    float randomFloat(float min, float max) {
        return min + randomFloat() * (max - min);
    }

    int randomInt(int min, int max) {
        std::uniform_int_distribution<int> dist(min, max);
        return dist(generator);
    }

    bool randomBool() {
        return boolDist(generator) == 1;
    }
}

} // namespace Math

void initialize() {
    // Initialize math library
    Math::Random::setSeed(static_cast<uint32>(std::time(nullptr)));
}

void shutdown() {
    // Cleanup math library
}

bool isSimdSupported() {
    // SIMD support detection will be implemented in future phases
    return false;
}

const char* getSimdInstructionSet() {
    return "None";
}

} // namespace PlaytimeEngine
