#pragma once

/**
 * @file Vector.h
 * @brief Vector mathematics classes for 2D, 3D, and 4D operations
 * 
 * Provides high-performance vector classes with SIMD optimization
 * for common game engine mathematical operations.
 */

#include <cmath>
#include <cassert>

#ifdef _WIN32
    #include <immintrin.h>
#elif defined(__ARM_NEON)
    #include <arm_neon.h>
#elif defined(__SSE__)
    #include <immintrin.h>
#endif

namespace PlaytimeEngine {
namespace Math {

/**
 * @brief 2D Vector class
 */
class Vector2 {
public:
    float x, y;

    // Constructors
    Vector2() : x(0.0f), y(0.0f) {}
    Vector2(float x, float y) : x(x), y(y) {}
    explicit Vector2(float scalar) : x(scalar), y(scalar) {}

    // Copy and assignment
    Vector2(const Vector2& other) = default;
    Vector2& operator=(const Vector2& other) = default;

    // Array access
    float& operator[](int index) {
        assert(index >= 0 && index < 2);
        return (&x)[index];
    }
    
    const float& operator[](int index) const {
        assert(index >= 0 && index < 2);
        return (&x)[index];
    }

    // Arithmetic operators
    Vector2 operator+(const Vector2& other) const { return Vector2(x + other.x, y + other.y); }
    Vector2 operator-(const Vector2& other) const { return Vector2(x - other.x, y - other.y); }
    Vector2 operator*(const Vector2& other) const { return Vector2(x * other.x, y * other.y); }
    Vector2 operator/(const Vector2& other) const { return Vector2(x / other.x, y / other.y); }
    
    Vector2 operator*(float scalar) const { return Vector2(x * scalar, y * scalar); }
    Vector2 operator/(float scalar) const { return Vector2(x / scalar, y / scalar); }
    
    Vector2 operator-() const { return Vector2(-x, -y); }

    // Assignment operators
    Vector2& operator+=(const Vector2& other) { x += other.x; y += other.y; return *this; }
    Vector2& operator-=(const Vector2& other) { x -= other.x; y -= other.y; return *this; }
    Vector2& operator*=(const Vector2& other) { x *= other.x; y *= other.y; return *this; }
    Vector2& operator/=(const Vector2& other) { x /= other.x; y /= other.y; return *this; }
    Vector2& operator*=(float scalar) { x *= scalar; y *= scalar; return *this; }
    Vector2& operator/=(float scalar) { x /= scalar; y /= scalar; return *this; }

    // Comparison operators
    bool operator==(const Vector2& other) const;
    bool operator!=(const Vector2& other) const { return !(*this == other); }

    // Vector operations
    float dot(const Vector2& other) const { return x * other.x + y * other.y; }
    float cross(const Vector2& other) const { return x * other.y - y * other.x; }
    float length() const { return std::sqrt(x * x + y * y); }
    float lengthSquared() const { return x * x + y * y; }
    float distance(const Vector2& other) const { return (*this - other).length(); }
    float distanceSquared(const Vector2& other) const { return (*this - other).lengthSquared(); }
    
    Vector2 normalized() const;
    Vector2& normalize();
    Vector2 perpendicular() const { return Vector2(-y, x); }
    
    // Static utility functions
    static Vector2 lerp(const Vector2& a, const Vector2& b, float t);
    static Vector2 slerp(const Vector2& a, const Vector2& b, float t);
    static Vector2 min(const Vector2& a, const Vector2& b);
    static Vector2 max(const Vector2& a, const Vector2& b);
    static Vector2 clamp(const Vector2& value, const Vector2& min, const Vector2& max);
    static Vector2 reflect(const Vector2& incident, const Vector2& normal);
    
    // Constants
    static const Vector2 ZERO;
    static const Vector2 ONE;
    static const Vector2 UNIT_X;
    static const Vector2 UNIT_Y;
};

/**
 * @brief 3D Vector class with SIMD optimization
 */
class Vector3 {
public:
    union {
        struct { float x, y, z; };
        float data[3];
#ifdef __SSE__
        __m128 simd;
#endif
    };

    // Constructors
    Vector3() : x(0.0f), y(0.0f), z(0.0f) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    explicit Vector3(float scalar) : x(scalar), y(scalar), z(scalar) {}
    Vector3(const Vector2& xy, float z) : x(xy.x), y(xy.y), z(z) {}

    // Copy and assignment
    Vector3(const Vector3& other) = default;
    Vector3& operator=(const Vector3& other) = default;

    // Array access
    float& operator[](int index) {
        assert(index >= 0 && index < 3);
        return data[index];
    }
    
    const float& operator[](int index) const {
        assert(index >= 0 && index < 3);
        return data[index];
    }

    // Arithmetic operators
    Vector3 operator+(const Vector3& other) const;
    Vector3 operator-(const Vector3& other) const;
    Vector3 operator*(const Vector3& other) const;
    Vector3 operator/(const Vector3& other) const;
    Vector3 operator*(float scalar) const;
    Vector3 operator/(float scalar) const;
    Vector3 operator-() const;

    // Assignment operators
    Vector3& operator+=(const Vector3& other);
    Vector3& operator-=(const Vector3& other);
    Vector3& operator*=(const Vector3& other);
    Vector3& operator/=(const Vector3& other);
    Vector3& operator*=(float scalar);
    Vector3& operator/=(float scalar);

    // Comparison operators
    bool operator==(const Vector3& other) const;
    bool operator!=(const Vector3& other) const { return !(*this == other); }

    // Vector operations
    float dot(const Vector3& other) const;
    Vector3 cross(const Vector3& other) const;
    float length() const;
    float lengthSquared() const;
    float distance(const Vector3& other) const;
    float distanceSquared(const Vector3& other) const;
    
    Vector3 normalized() const;
    Vector3& normalize();
    
    // Swizzling
    Vector2 xy() const { return Vector2(x, y); }
    Vector2 xz() const { return Vector2(x, z); }
    Vector2 yz() const { return Vector2(y, z); }
    
    // Static utility functions
    static Vector3 lerp(const Vector3& a, const Vector3& b, float t);
    static Vector3 slerp(const Vector3& a, const Vector3& b, float t);
    static Vector3 min(const Vector3& a, const Vector3& b);
    static Vector3 max(const Vector3& a, const Vector3& b);
    static Vector3 clamp(const Vector3& value, const Vector3& min, const Vector3& max);
    static Vector3 reflect(const Vector3& incident, const Vector3& normal);
    static Vector3 refract(const Vector3& incident, const Vector3& normal, float eta);
    
    // Constants
    static const Vector3 ZERO;
    static const Vector3 ONE;
    static const Vector3 UNIT_X;
    static const Vector3 UNIT_Y;
    static const Vector3 UNIT_Z;
    static const Vector3 UP;
    static const Vector3 DOWN;
    static const Vector3 LEFT;
    static const Vector3 RIGHT;
    static const Vector3 FORWARD;
    static const Vector3 BACKWARD;
};

/**
 * @brief 4D Vector class with SIMD optimization
 */
class Vector4 {
public:
    union {
        struct { float x, y, z, w; };
        float data[4];
#ifdef __SSE__
        __m128 simd;
#endif
    };

    // Constructors
    Vector4() : x(0.0f), y(0.0f), z(0.0f), w(0.0f) {}
    Vector4(float x, float y, float z, float w) : x(x), y(y), z(z), w(w) {}
    explicit Vector4(float scalar) : x(scalar), y(scalar), z(scalar), w(scalar) {}
    Vector4(const Vector3& xyz, float w) : x(xyz.x), y(xyz.y), z(xyz.z), w(w) {}
    Vector4(const Vector2& xy, const Vector2& zw) : x(xy.x), y(xy.y), z(zw.x), w(zw.y) {}

#ifdef __SSE__
    Vector4(__m128 simd) : simd(simd) {}
#endif

    // Copy and assignment
    Vector4(const Vector4& other) = default;
    Vector4& operator=(const Vector4& other) = default;

    // Array access
    float& operator[](int index) {
        assert(index >= 0 && index < 4);
        return data[index];
    }
    
    const float& operator[](int index) const {
        assert(index >= 0 && index < 4);
        return data[index];
    }

    // Arithmetic operators (SIMD optimized)
    Vector4 operator+(const Vector4& other) const;
    Vector4 operator-(const Vector4& other) const;
    Vector4 operator*(const Vector4& other) const;
    Vector4 operator/(const Vector4& other) const;
    Vector4 operator*(float scalar) const;
    Vector4 operator/(float scalar) const;
    Vector4 operator-() const;

    // Assignment operators
    Vector4& operator+=(const Vector4& other);
    Vector4& operator-=(const Vector4& other);
    Vector4& operator*=(const Vector4& other);
    Vector4& operator/=(const Vector4& other);
    Vector4& operator*=(float scalar);
    Vector4& operator/=(float scalar);

    // Comparison operators
    bool operator==(const Vector4& other) const;
    bool operator!=(const Vector4& other) const { return !(*this == other); }

    // Vector operations
    float dot(const Vector4& other) const;
    float length() const;
    float lengthSquared() const;
    float distance(const Vector4& other) const;
    float distanceSquared(const Vector4& other) const;
    
    Vector4 normalized() const;
    Vector4& normalize();
    
    // Swizzling
    Vector2 xy() const { return Vector2(x, y); }
    Vector2 zw() const { return Vector2(z, w); }
    Vector3 xyz() const { return Vector3(x, y, z); }
    
    // Static utility functions
    static Vector4 lerp(const Vector4& a, const Vector4& b, float t);
    static Vector4 min(const Vector4& a, const Vector4& b);
    static Vector4 max(const Vector4& a, const Vector4& b);
    static Vector4 clamp(const Vector4& value, const Vector4& min, const Vector4& max);
    
    // Constants
    static const Vector4 ZERO;
    static const Vector4 ONE;
    static const Vector4 UNIT_X;
    static const Vector4 UNIT_Y;
    static const Vector4 UNIT_Z;
    static const Vector4 UNIT_W;
};

// Global operators for scalar multiplication
Vector2 operator*(float scalar, const Vector2& vector);
Vector3 operator*(float scalar, const Vector3& vector);
Vector4 operator*(float scalar, const Vector4& vector);

} // namespace Math
} // namespace PlaytimeEngine
