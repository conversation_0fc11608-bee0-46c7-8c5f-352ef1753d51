/**
 * @file Quaternion.cpp
 * @brief Quaternion implementation
 */

#include "Quaternion.h"
#include "Matrix.h"
#include <cmath>

namespace PlaytimeEngine {
namespace Math {

// Quaternion constants
const Quaternion Quaternion::IDENTITY(0.0f, 0.0f, 0.0f, 1.0f);
const Quaternion Quaternion::ZERO(0.0f, 0.0f, 0.0f, 0.0f);

// Quaternion implementation
Quaternion::Quaternion(const Vector3& axis, float angle) {
    float halfAngle = angle * 0.5f;
    float sinHalf = std::sin(halfAngle);
    float cosHalf = std::cos(halfAngle);
    
    Vector3 normalizedAxis = axis.normalized();
    x = normalizedAxis.x * sinHalf;
    y = normalizedAxis.y * sinHalf;
    z = normalizedAxis.z * sinHalf;
    w = cosHalf;
}

bool Quaternion::operator==(const Quaternion& other) const {
    return std::abs(x - other.x) < EPSILON &&
           std::abs(y - other.y) < EPSILON &&
           std::abs(z - other.z) < EPSILON &&
           std::abs(w - other.w) < EPSILON;
}

Quaternion Quaternion::operator+(const Quaternion& other) const {
    return Quaternion(x + other.x, y + other.y, z + other.z, w + other.w);
}

Quaternion Quaternion::operator*(const Quaternion& other) const {
    return Quaternion(
        w * other.x + x * other.w + y * other.z - z * other.y,
        w * other.y - x * other.z + y * other.w + z * other.x,
        w * other.z + x * other.y - y * other.x + z * other.w,
        w * other.w - x * other.x - y * other.y - z * other.z
    );
}

Quaternion Quaternion::operator*(float scalar) const {
    return Quaternion(x * scalar, y * scalar, z * scalar, w * scalar);
}

Vector3 Quaternion::operator*(const Vector3& vector) const {
    return rotate(vector);
}

Vector3 Quaternion::rotate(const Vector3& vector) const {
    // Using the formula: v' = q * v * q^-1
    // Optimized version: v' = v + 2 * cross(q.xyz, cross(q.xyz, v) + q.w * v)
    Vector3 qvec(x, y, z);
    Vector3 uv = qvec.cross(vector);
    Vector3 uuv = qvec.cross(uv);
    
    return vector + 2.0f * (uv * w + uuv);
}

float Quaternion::dot(const Quaternion& other) const {
    return x * other.x + y * other.y + z * other.z + w * other.w;
}

float Quaternion::length() const {
    return std::sqrt(x * x + y * y + z * z + w * w);
}

float Quaternion::lengthSquared() const {
    return x * x + y * y + z * z + w * w;
}

Quaternion Quaternion::normalized() const {
    float len = length();
    if (len > EPSILON) {
        return Quaternion(x / len, y / len, z / len, w / len);
    }
    return Quaternion::IDENTITY;
}

Quaternion& Quaternion::normalize() {
    float len = length();
    if (len > EPSILON) {
        x /= len;
        y /= len;
        z /= len;
        w /= len;
    }
    return *this;
}

Quaternion Quaternion::conjugate() const {
    return Quaternion(-x, -y, -z, w);
}

Quaternion Quaternion::inverse() const {
    float lenSq = lengthSquared();
    if (lenSq > EPSILON) {
        Quaternion conj = conjugate();
        return conj * (1.0f / lenSq);
    }
    return Quaternion::IDENTITY;
}

Matrix4x4 Quaternion::toMatrix4x4() const {
    float xx = x * x;
    float yy = y * y;
    float zz = z * z;
    float xy = x * y;
    float xz = x * z;
    float yz = y * z;
    float wx = w * x;
    float wy = w * y;
    float wz = w * z;
    
    return Matrix4x4(
        1.0f - 2.0f * (yy + zz), 2.0f * (xy - wz), 2.0f * (xz + wy), 0.0f,
        2.0f * (xy + wz), 1.0f - 2.0f * (xx + zz), 2.0f * (yz - wx), 0.0f,
        2.0f * (xz - wy), 2.0f * (yz + wx), 1.0f - 2.0f * (xx + yy), 0.0f,
        0.0f, 0.0f, 0.0f, 1.0f
    );
}

Quaternion Quaternion::rotationY(float angle) {
    float halfAngle = angle * 0.5f;
    return Quaternion(0.0f, std::sin(halfAngle), 0.0f, std::cos(halfAngle));
}

Quaternion Quaternion::slerp(const Quaternion& a, const Quaternion& b, float t) {
    float dot = a.dot(b);
    
    // If the dot product is negative, slerp won't take the shorter path.
    // Note that v1 and -v1 are equivalent when the represent rotations.
    Quaternion b_copy = b;
    if (dot < 0.0f) {
        b_copy = Quaternion(-b.x, -b.y, -b.z, -b.w);
        dot = -dot;
    }
    
    // If the inputs are too close for comfort, linearly interpolate
    if (dot > 0.9995f) {
        Quaternion result = a + t * (b_copy - a);
        return result.normalized();
    }
    
    // Since dot is in range [0, 1], acos is safe
    float theta_0 = std::acos(dot);
    float theta = theta_0 * t;
    float sin_theta = std::sin(theta);
    float sin_theta_0 = std::sin(theta_0);
    
    float s0 = std::cos(theta) - dot * sin_theta / sin_theta_0;
    float s1 = sin_theta / sin_theta_0;
    
    return s0 * a + s1 * b_copy;
}

// Global operators
Quaternion operator*(float scalar, const Quaternion& quaternion) {
    return quaternion * scalar;
}

} // namespace Math
} // namespace PlaytimeEngine
