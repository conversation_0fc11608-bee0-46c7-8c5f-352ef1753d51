/**
 * @file Vector.cpp
 * @brief Vector implementation
 */

#include "Vector.h"
#include <cmath>

namespace PlaytimeEngine {
namespace Math {

// Vector2 constants
const Vector2 Vector2::ZERO(0.0f, 0.0f);
const Vector2 Vector2::ONE(1.0f, 1.0f);
const Vector2 Vector2::UNIT_X(1.0f, 0.0f);
const Vector2 Vector2::UNIT_Y(0.0f, 1.0f);

// Vector2 implementation
bool Vector2::operator==(const Vector2& other) const {
    return std::abs(x - other.x) < EPSILON && std::abs(y - other.y) < EPSILON;
}

Vector2 Vector2::normalized() const {
    float len = length();
    if (len > EPSILON) {
        return Vector2(x / len, y / len);
    }
    return Vector2::ZERO;
}

Vector2& Vector2::normalize() {
    float len = length();
    if (len > EPSILON) {
        x /= len;
        y /= len;
    }
    return *this;
}

Vector2 Vector2::lerp(const Vector2& a, const Vector2& b, float t) {
    return a + t * (b - a);
}

Vector2 Vector2::slerp(const Vector2& a, const Vector2& b, float t) {
    // For 2D vectors, slerp is the same as lerp for unit vectors
    return lerp(a, b, t);
}

Vector2 Vector2::min(const Vector2& a, const Vector2& b) {
    return Vector2(std::min(a.x, b.x), std::min(a.y, b.y));
}

Vector2 Vector2::max(const Vector2& a, const Vector2& b) {
    return Vector2(std::max(a.x, b.x), std::max(a.y, b.y));
}

Vector2 Vector2::clamp(const Vector2& value, const Vector2& min, const Vector2& max) {
    return Vector2(
        std::clamp(value.x, min.x, max.x),
        std::clamp(value.y, min.y, max.y)
    );
}

Vector2 Vector2::reflect(const Vector2& incident, const Vector2& normal) {
    return incident - 2.0f * incident.dot(normal) * normal;
}

// Vector3 constants
const Vector3 Vector3::ZERO(0.0f, 0.0f, 0.0f);
const Vector3 Vector3::ONE(1.0f, 1.0f, 1.0f);
const Vector3 Vector3::UNIT_X(1.0f, 0.0f, 0.0f);
const Vector3 Vector3::UNIT_Y(0.0f, 1.0f, 0.0f);
const Vector3 Vector3::UNIT_Z(0.0f, 0.0f, 1.0f);
const Vector3 Vector3::UP(0.0f, 1.0f, 0.0f);
const Vector3 Vector3::DOWN(0.0f, -1.0f, 0.0f);
const Vector3 Vector3::LEFT(-1.0f, 0.0f, 0.0f);
const Vector3 Vector3::RIGHT(1.0f, 0.0f, 0.0f);
const Vector3 Vector3::FORWARD(0.0f, 0.0f, 1.0f);
const Vector3 Vector3::BACKWARD(0.0f, 0.0f, -1.0f);

// Vector3 basic operations (placeholder implementations)
Vector3 Vector3::operator+(const Vector3& other) const {
    return Vector3(x + other.x, y + other.y, z + other.z);
}

Vector3 Vector3::operator-(const Vector3& other) const {
    return Vector3(x - other.x, y - other.y, z - other.z);
}

Vector3 Vector3::operator*(const Vector3& other) const {
    return Vector3(x * other.x, y * other.y, z * other.z);
}

Vector3 Vector3::operator*(float scalar) const {
    return Vector3(x * scalar, y * scalar, z * scalar);
}

Vector3 Vector3::operator-() const {
    return Vector3(-x, -y, -z);
}

bool Vector3::operator==(const Vector3& other) const {
    return std::abs(x - other.x) < EPSILON && 
           std::abs(y - other.y) < EPSILON && 
           std::abs(z - other.z) < EPSILON;
}

float Vector3::dot(const Vector3& other) const {
    return x * other.x + y * other.y + z * other.z;
}

Vector3 Vector3::cross(const Vector3& other) const {
    return Vector3(
        y * other.z - z * other.y,
        z * other.x - x * other.z,
        x * other.y - y * other.x
    );
}

float Vector3::length() const {
    return std::sqrt(x * x + y * y + z * z);
}

float Vector3::lengthSquared() const {
    return x * x + y * y + z * z;
}

Vector3 Vector3::normalized() const {
    float len = length();
    if (len > EPSILON) {
        return Vector3(x / len, y / len, z / len);
    }
    return Vector3::ZERO;
}

// Vector4 constants
const Vector4 Vector4::ZERO(0.0f, 0.0f, 0.0f, 0.0f);
const Vector4 Vector4::ONE(1.0f, 1.0f, 1.0f, 1.0f);
const Vector4 Vector4::UNIT_X(1.0f, 0.0f, 0.0f, 0.0f);
const Vector4 Vector4::UNIT_Y(0.0f, 1.0f, 0.0f, 0.0f);
const Vector4 Vector4::UNIT_Z(0.0f, 0.0f, 1.0f, 0.0f);
const Vector4 Vector4::UNIT_W(0.0f, 0.0f, 0.0f, 1.0f);

// Vector4 basic operations (placeholder implementations)
Vector4 Vector4::operator+(const Vector4& other) const {
    return Vector4(x + other.x, y + other.y, z + other.z, w + other.w);
}

bool Vector4::operator==(const Vector4& other) const {
    return std::abs(x - other.x) < EPSILON && 
           std::abs(y - other.y) < EPSILON && 
           std::abs(z - other.z) < EPSILON && 
           std::abs(w - other.w) < EPSILON;
}

float Vector4::dot(const Vector4& other) const {
    return x * other.x + y * other.y + z * other.z + w * other.w;
}

float Vector4::length() const {
    return std::sqrt(x * x + y * y + z * z + w * w);
}

// Global operators
Vector2 operator*(float scalar, const Vector2& vector) {
    return vector * scalar;
}

Vector3 operator*(float scalar, const Vector3& vector) {
    return vector * scalar;
}

Vector4 operator*(float scalar, const Vector4& vector) {
    return vector * scalar;
}

} // namespace Math
} // namespace PlaytimeEngine
