#pragma once

/**
 * @file Quaternion.h
 * @brief Quaternion class for efficient 3D rotations
 * 
 * Provides a high-performance quaternion implementation for representing
 * and manipulating 3D rotations in the game engine.
 */

#include "Vector.h"

namespace PlaytimeEngine {
namespace Math {

// Forward declaration
class Matrix4x4;
class Matrix3x3;

/**
 * @brief Quaternion class for representing 3D rotations
 * 
 * Quaternions provide a compact and efficient way to represent rotations
 * without gimbal lock issues. They are particularly useful for animation
 * interpolation and composition of rotations.
 */
class Quaternion {
public:
    union {
        struct { float x, y, z, w; };
        float data[4];
#ifdef __SSE__
        __m128 simd;
#endif
    };

    // Constructors
    Quaternion() : x(0.0f), y(0.0f), z(0.0f), w(1.0f) {}
    Quaternion(float x, float y, float z, float w) : x(x), y(y), z(z), w(w) {}
    explicit Quaternion(const Vector4& vector) : x(vector.x), y(vector.y), z(vector.z), w(vector.w) {}
    Quaternion(const Vector3& axis, float angle);
    explicit Quaternion(const Vector3& eulerAngles);
    explicit Quaternion(const Matrix3x3& rotationMatrix);
    explicit Quaternion(const Matrix4x4& transformMatrix);

#ifdef __SSE__
    Quaternion(__m128 simd) : simd(simd) {}
#endif

    // Copy and assignment
    Quaternion(const Quaternion& other) = default;
    Quaternion& operator=(const Quaternion& other) = default;

    // Array access
    float& operator[](int index) {
        assert(index >= 0 && index < 4);
        return data[index];
    }
    
    const float& operator[](int index) const {
        assert(index >= 0 && index < 4);
        return data[index];
    }

    // Arithmetic operators
    Quaternion operator+(const Quaternion& other) const;
    Quaternion operator-(const Quaternion& other) const;
    Quaternion operator*(const Quaternion& other) const;
    Quaternion operator*(float scalar) const;
    Quaternion operator/(float scalar) const;
    Quaternion operator-() const;

    // Assignment operators
    Quaternion& operator+=(const Quaternion& other);
    Quaternion& operator-=(const Quaternion& other);
    Quaternion& operator*=(const Quaternion& other);
    Quaternion& operator*=(float scalar);
    Quaternion& operator/=(float scalar);

    // Comparison operators
    bool operator==(const Quaternion& other) const;
    bool operator!=(const Quaternion& other) const { return !(*this == other); }

    // Vector rotation
    Vector3 operator*(const Vector3& vector) const;
    Vector3 rotate(const Vector3& vector) const;
    Vector3 rotateInverse(const Vector3& vector) const;

    // Quaternion operations
    float dot(const Quaternion& other) const;
    float length() const;
    float lengthSquared() const;
    
    Quaternion normalized() const;
    Quaternion& normalize();
    Quaternion conjugate() const;
    Quaternion inverse() const;
    
    // Conversion methods
    Vector3 toEulerAngles() const;
    Vector3 toAxisAngle(float& angle) const;
    Matrix3x3 toMatrix3x3() const;
    Matrix4x4 toMatrix4x4() const;
    Vector4 toVector4() const { return Vector4(x, y, z, w); }

    // Utility methods
    bool isNormalized(float tolerance = 1e-6f) const;
    bool isIdentity(float tolerance = 1e-6f) const;
    float getAngle() const;
    Vector3 getAxis() const;

    // Static utility functions
    static Quaternion lerp(const Quaternion& a, const Quaternion& b, float t);
    static Quaternion slerp(const Quaternion& a, const Quaternion& b, float t);
    static Quaternion slerpShortest(const Quaternion& a, const Quaternion& b, float t);
    static Quaternion nlerp(const Quaternion& a, const Quaternion& b, float t);
    
    static Quaternion lookRotation(const Vector3& forward, const Vector3& up = Vector3::UP);
    static Quaternion fromToRotation(const Vector3& from, const Vector3& to);
    static Quaternion angleAxis(float angle, const Vector3& axis);
    static Quaternion eulerAngles(float pitch, float yaw, float roll);
    static Quaternion eulerAngles(const Vector3& eulerAngles);
    
    // Composition and decomposition
    static Quaternion compose(const Quaternion& a, const Quaternion& b) { return a * b; }
    static float angleBetween(const Quaternion& a, const Quaternion& b);
    
    // Constants
    static const Quaternion IDENTITY;
    static const Quaternion ZERO;

    // Static factory methods for common rotations
    static Quaternion rotationX(float angle);
    static Quaternion rotationY(float angle);
    static Quaternion rotationZ(float angle);
};

// Global operators
Quaternion operator*(float scalar, const Quaternion& quaternion);

// Utility functions
namespace QuaternionUtils {
    /**
     * @brief Convert Euler angles to quaternion using specified rotation order
     */
    enum class RotationOrder {
        XYZ, XZY, YXZ, YZX, ZXY, ZYX
    };
    
    Quaternion fromEulerAngles(const Vector3& eulerAngles, RotationOrder order = RotationOrder::YXZ);
    Vector3 toEulerAngles(const Quaternion& quaternion, RotationOrder order = RotationOrder::YXZ);
    
    /**
     * @brief Spherical linear interpolation with multiple spins
     */
    Quaternion slerpWithSpin(const Quaternion& a, const Quaternion& b, float t, int spins);
    
    /**
     * @brief Squad interpolation for smooth quaternion curves
     */
    Quaternion squad(const Quaternion& q0, const Quaternion& q1, const Quaternion& q2, const Quaternion& q3, float t);
    
    /**
     * @brief Compute intermediate quaternions for squad interpolation
     */
    void computeSquadIntermediates(const Quaternion& q0, const Quaternion& q1, const Quaternion& q2, 
                                   Quaternion& outA, Quaternion& outB);
}

} // namespace Math
} // namespace PlaytimeEngine
