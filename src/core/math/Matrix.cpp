/**
 * @file Matrix.cpp
 * @brief Matrix implementation
 */

#include "Matrix.h"
#include <cstring>

namespace PlaytimeEngine {
namespace Math {

// Matrix3x3 constants
const Matrix3x3 Matrix3x3::IDENTITY = Matrix3x3::identity();
const Matrix3x3 Matrix3x3::ZERO = Matrix3x3::zero();

// Matrix3x3 implementation
Matrix3x3::Matrix3x3() {
    std::memset(m, 0, sizeof(m));
}

Matrix3x3::Matrix3x3(float m00, float m01, float m02,
                     float m10, float m11, float m12,
                     float m20, float m21, float m22) {
    m[0] = m00; m[1] = m01; m[2] = m02;
    m[3] = m10; m[4] = m11; m[5] = m12;
    m[6] = m20; m[7] = m21; m[8] = m22;
}

Matrix3x3::Matrix3x3(const float* data) {
    std::memcpy(m, data, sizeof(m));
}

Matrix3x3 Matrix3x3::identity() {
    return Matrix3x3(
        1.0f, 0.0f, 0.0f,
        0.0f, 1.0f, 0.0f,
        0.0f, 0.0f, 1.0f
    );
}

Matrix3x3 Matrix3x3::zero() {
    return Matrix3x3(
        0.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 0.0f
    );
}

bool Matrix3x3::operator==(const Matrix3x3& other) const {
    for (int i = 0; i < 9; ++i) {
        if (std::abs(m[i] - other.m[i]) > EPSILON) {
            return false;
        }
    }
    return true;
}

// Matrix4x4 constants
const Matrix4x4 Matrix4x4::IDENTITY = Matrix4x4::identity();
const Matrix4x4 Matrix4x4::ZERO = Matrix4x4::zero();

// Matrix4x4 implementation
Matrix4x4::Matrix4x4() {
    std::memset(m, 0, sizeof(m));
}

Matrix4x4::Matrix4x4(float m00, float m01, float m02, float m03,
                     float m10, float m11, float m12, float m13,
                     float m20, float m21, float m22, float m23,
                     float m30, float m31, float m32, float m33) {
    m[0] = m00; m[1] = m01; m[2] = m02; m[3] = m03;
    m[4] = m10; m[5] = m11; m[6] = m12; m[7] = m13;
    m[8] = m20; m[9] = m21; m[10] = m22; m[11] = m23;
    m[12] = m30; m[13] = m31; m[14] = m32; m[15] = m33;
}

Matrix4x4::Matrix4x4(const float* data) {
    std::memcpy(m, data, sizeof(m));
}

Matrix4x4 Matrix4x4::identity() {
    return Matrix4x4(
        1.0f, 0.0f, 0.0f, 0.0f,
        0.0f, 1.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 1.0f, 0.0f,
        0.0f, 0.0f, 0.0f, 1.0f
    );
}

Matrix4x4 Matrix4x4::zero() {
    return Matrix4x4(
        0.0f, 0.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 0.0f, 0.0f
    );
}

Matrix4x4 Matrix4x4::translation(float tx, float ty, float tz) {
    return Matrix4x4(
        1.0f, 0.0f, 0.0f, tx,
        0.0f, 1.0f, 0.0f, ty,
        0.0f, 0.0f, 1.0f, tz,
        0.0f, 0.0f, 0.0f, 1.0f
    );
}

Matrix4x4 Matrix4x4::translation(const Vector3& translation) {
    return Matrix4x4::translation(translation.x, translation.y, translation.z);
}

Vector3 Matrix4x4::transformPoint(const Vector3& point) const {
    float x = m[0] * point.x + m[1] * point.y + m[2] * point.z + m[3];
    float y = m[4] * point.x + m[5] * point.y + m[6] * point.z + m[7];
    float z = m[8] * point.x + m[9] * point.y + m[10] * point.z + m[11];
    float w = m[12] * point.x + m[13] * point.y + m[14] * point.z + m[15];
    
    if (std::abs(w) > EPSILON) {
        return Vector3(x / w, y / w, z / w);
    }
    return Vector3(x, y, z);
}

bool Matrix4x4::operator==(const Matrix4x4& other) const {
    for (int i = 0; i < 16; ++i) {
        if (std::abs(m[i] - other.m[i]) > EPSILON) {
            return false;
        }
    }
    return true;
}

} // namespace Math
} // namespace PlaytimeEngine
