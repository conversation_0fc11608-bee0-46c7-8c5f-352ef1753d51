#pragma once

/**
 * @file String.h
 * @brief String utilities and custom string class
 */

#include "Types.h"
#include <string>
#include <vector>

namespace PlaytimeEngine {

/**
 * @brief String utility functions
 */
namespace StringUtils {
    /**
     * @brief Convert string to lowercase
     */
    ENGINE_API String toLower(const String& str);
    
    /**
     * @brief Convert string to uppercase
     */
    ENGINE_API String toUpper(const String& str);
    
    /**
     * @brief Trim whitespace from both ends
     */
    ENGINE_API String trim(const String& str);
    
    /**
     * @brief Split string by delimiter
     */
    ENGINE_API std::vector<String> split(const String& str, char delimiter);
    
    /**
     * @brief Join strings with delimiter
     */
    ENGINE_API String join(const std::vector<String>& strings, const String& delimiter);
    
    /**
     * @brief Check if string starts with prefix
     */
    ENGINE_API bool startsWith(const String& str, const String& prefix);
    
    /**
     * @brief Check if string ends with suffix
     */
    ENGINE_API bool endsWith(const String& str, const String& suffix);
    
    /**
     * @brief Replace all occurrences of a substring
     */
    ENGINE_API String replace(const String& str, const String& from, const String& to);
    
    /**
     * @brief Format string (printf-style)
     */
    ENGINE_API String format(const char* format, ...);
    
    /**
     * @brief Convert to string
     */
    template<typename T>
    String toString(const T& value) {
        return std::to_string(value);
    }
    
    /**
     * @brief Parse from string
     */
    template<typename T>
    T fromString(const String& str);
    
    // Template specializations
    template<> ENGINE_API int fromString<int>(const String& str);
    template<> ENGINE_API float fromString<float>(const String& str);
    template<> ENGINE_API double fromString<double>(const String& str);
    template<> ENGINE_API bool fromString<bool>(const String& str);
}

/**
 * @brief Hash function for strings
 */
ENGINE_API Hash hashString(const String& str);
ENGINE_API Hash hashString(const char* str);

} // namespace PlaytimeEngine
