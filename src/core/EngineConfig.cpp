/**
 * @file EngineConfig.cpp
 * @brief Engine configuration implementation
 */

#include "EngineConfig.h"
#include "Logger.h"

namespace PlaytimeEngine {

bool EngineConfig::loadFromFile(const String& filename) {
    // Configuration file loading will be implemented in future phases
    LOG_INFO("Loading configuration from file: %s", filename.c_str());
    (void)filename;
    return true;
}

bool EngineConfig::saveToFile(const String& filename) const {
    // Configuration file saving will be implemented in future phases
    LOG_INFO("Saving configuration to file: %s", filename.c_str());
    (void)filename;
    return true;
}

EngineConfig EngineConfig::getDefault() {
    EngineConfig config;
    
    // Set default values
    config.logFile = "engine.log";
    config.logLevel = 3; // Info level
    config.debugMode = false;
    
    // Rendering defaults
    config.renderConfig.windowWidth = 1920;
    config.renderConfig.windowHeight = 1080;
    config.renderConfig.fullscreen = false;
    config.renderConfig.vsync = true;
    config.renderConfig.maxFrameRate = 60;
    config.renderConfig.windowTitle = "Project Playtime Engine";
    
    // Audio defaults
    config.audioConfig.sampleRate = 44100;
    config.audioConfig.bufferSize = 1024;
    config.audioConfig.channels = 2;
    config.audioConfig.masterVolume = 1.0f;
    
    // Physics defaults
    config.physicsConfig.gravity = -9.81f;
    config.physicsConfig.maxSubSteps = 8;
    config.physicsConfig.fixedTimeStep = 1.0f / 60.0f;
    config.physicsConfig.enableCCD = true;
    
    // Job system defaults
    config.workerThreadCount = 0; // Auto-detect
    
    // Memory defaults
    config.heapSize = 256 * MEGABYTE;
    config.stackSize = 16 * MEGABYTE;
    
    return config;
}

} // namespace PlaytimeEngine
