/**
 * @file String.cpp
 * @brief String utilities implementation
 */

#include "String.h"
#include "Hash.h"
#include <algorithm>
#include <cctype>
#include <sstream>
#include <cstdarg>
#include <limits>
#include <cstdlib>
#include <vector>

namespace PlaytimeEngine {
namespace StringUtils {

String toLower(const String& str) {
    String result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::tolower(c); });
    return result;
}

String toUpper(const String& str) {
    String result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::toupper(c); });
    return result;
}

String trim(const String& str) {
    if (str.empty()) {
        return str;
    }
    
    size_t start = str.find_first_not_of(" \t\n\r\f\v");
    if (start == String::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(start, end - start + 1);
}

std::vector<String> split(const String& str, char delimiter) {
    std::vector<String> result;
    std::stringstream ss(str);
    String item;
    
    while (std::getline(ss, item, delimiter)) {
        result.push_back(item);
    }
    
    return result;
}

String join(const std::vector<String>& strings, const String& delimiter) {
    if (strings.empty()) {
        return "";
    }
    
    std::ostringstream oss;
    for (size_t i = 0; i < strings.size(); ++i) {
        if (i > 0) {
            oss << delimiter;
        }
        oss << strings[i];
    }
    
    return oss.str();
}

bool startsWith(const String& str, const String& prefix) {
    if (prefix.length() > str.length()) {
        return false;
    }
    return str.compare(0, prefix.length(), prefix) == 0;
}

bool endsWith(const String& str, const String& suffix) {
    if (suffix.length() > str.length()) {
        return false;
    }
    return str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

String replace(const String& str, const String& from, const String& to) {
    if (from.empty()) {
        return str;
    }
    
    String result = str;
    size_t pos = 0;
    
    while ((pos = result.find(from, pos)) != String::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    
    return result;
}

String format(const char* format, ...) {
    va_list args;
    va_start(args, format);
    
    // Get required buffer size
    va_list args_copy;
    va_copy(args_copy, args);
    int size = vsnprintf(nullptr, 0, format, args_copy);
    va_end(args_copy);
    
    if (size <= 0) {
        va_end(args);
        return "";
    }
    
    // Format the string
    std::vector<char> buffer(size + 1);
    vsnprintf(buffer.data(), buffer.size(), format, args);
    va_end(args);
    
    return String(buffer.data());
}

// Template specializations
template<>
int fromString<int>(const String& str) {
    if (str.empty()) return 0;
    
    char* endPtr;
    long result = std::strtol(str.c_str(), &endPtr, 10);
    
    // Check if conversion was successful and result fits in int
    if (endPtr == str.c_str() || *endPtr != '\0' || 
        result < std::numeric_limits<int>::min() || 
        result > std::numeric_limits<int>::max()) {
        return 0;
    }
    
    return static_cast<int>(result);
}

template<>
float fromString<float>(const String& str) {
    try {
        return std::stof(str);
    } catch (const std::exception&) {
        return 0.0f;
    }
}

template<>
double fromString<double>(const String& str) {
    try {
        return std::stod(str);
    } catch (const std::exception&) {
        return 0.0;
    }
}

template<>
bool fromString<bool>(const String& str) {
    String lower = toLower(str);
    return (lower == "true" || lower == "1" || lower == "yes" || lower == "on");
}

} // namespace StringUtils

// Hash functions
Hash hashString(const String& str) {
    return hashString(str.c_str());
}

Hash hashString(const char* str) {
    // FNV-1a hash algorithm
    constexpr Hash FNV_OFFSET_BASIS = 14695981039346656037ULL;
    constexpr Hash FNV_PRIME = 1099511628211ULL;
    
    Hash hash = FNV_OFFSET_BASIS;
    
    while (*str) {
        hash ^= static_cast<Hash>(*str++);
        hash *= FNV_PRIME;
    }
    
    return hash;
}

} // namespace PlaytimeEngine
