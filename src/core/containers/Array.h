#pragma once

/**
 * @file Array.h
 * @brief Dynamic array container with custom allocator support
 */

#include "../Types.h"
#include <vector>
#include <initializer_list>

namespace PlaytimeEngine {

// Forward declaration
class IAllocator;

/**
 * @brief Dynamic array container with custom allocator support
 */
template<typename T>
class Array {
public:
    using value_type = T;
    using size_type = size_t;
    using difference_type = ptrdiff_t;
    using reference = T&;
    using const_reference = const T&;
    using pointer = T*;
    using const_pointer = const T*;
    using iterator = T*;
    using const_iterator = const T*;

    /**
     * @brief Default constructor
     */
    Array() : data_(nullptr), size_(0), capacity_(0), allocator_(nullptr) {}
    
    /**
     * @brief Constructor with allocator
     */
    explicit Array(IAllocator* allocator) : data_(nullptr), size_(0), capacity_(0), allocator_(allocator) {}
    
    /**
     * @brief Constructor with size
     */
    explicit Array(size_type count, IAllocator* allocator = nullptr) 
        : data_(nullptr), size_(0), capacity_(0), allocator_(allocator) {
        resize(count);
    }
    
    /**
     * @brief Constructor with size and default value
     */
    Array(size_type count, const T& value, IAllocator* allocator = nullptr)
        : data_(nullptr), size_(0), capacity_(0), allocator_(allocator) {
        resize(count, value);
    }
    
    /**
     * @brief Constructor with initializer list
     */
    Array(std::initializer_list<T> init, IAllocator* allocator = nullptr)
        : data_(nullptr), size_(0), capacity_(0), allocator_(allocator) {
        reserve(init.size());
        for (const auto& item : init) {
            pushBack(item);
        }
    }
    
    /**
     * @brief Copy constructor
     */
    Array(const Array& other) : data_(nullptr), size_(0), capacity_(0), allocator_(other.allocator_) {
        *this = other;
    }
    
    /**
     * @brief Move constructor
     */
    Array(Array&& other) noexcept : data_(other.data_), size_(other.size_), capacity_(other.capacity_), allocator_(other.allocator_) {
        other.data_ = nullptr;
        other.size_ = 0;
        other.capacity_ = 0;
    }
    
    /**
     * @brief Destructor
     */
    ~Array() {
        clear();
        deallocate();
    }
    
    /**
     * @brief Copy assignment
     */
    Array& operator=(const Array& other) {
        if (this != &other) {
            clear();
            reserve(other.size_);
            for (size_type i = 0; i < other.size_; ++i) {
                new (data_ + i) T(other.data_[i]);
            }
            size_ = other.size_;
        }
        return *this;
    }
    
    /**
     * @brief Move assignment
     */
    Array& operator=(Array&& other) noexcept {
        if (this != &other) {
            clear();
            deallocate();
            
            data_ = other.data_;
            size_ = other.size_;
            capacity_ = other.capacity_;
            allocator_ = other.allocator_;
            
            other.data_ = nullptr;
            other.size_ = 0;
            other.capacity_ = 0;
        }
        return *this;
    }
    
    // Element access
    reference operator[](size_type index) { return data_[index]; }
    const_reference operator[](size_type index) const { return data_[index]; }
    
    reference at(size_type index) {
        if (index >= size_) throw std::out_of_range("Array index out of range");
        return data_[index];
    }
    
    const_reference at(size_type index) const {
        if (index >= size_) throw std::out_of_range("Array index out of range");
        return data_[index];
    }
    
    reference front() { return data_[0]; }
    const_reference front() const { return data_[0]; }
    
    reference back() { return data_[size_ - 1]; }
    const_reference back() const { return data_[size_ - 1]; }
    
    pointer data() { return data_; }
    const_pointer data() const { return data_; }
    
    // Iterators
    iterator begin() { return data_; }
    const_iterator begin() const { return data_; }
    const_iterator cbegin() const { return data_; }
    
    iterator end() { return data_ + size_; }
    const_iterator end() const { return data_ + size_; }
    const_iterator cend() const { return data_ + size_; }
    
    // Capacity
    bool empty() const { return size_ == 0; }
    size_type size() const { return size_; }
    size_type capacity() const { return capacity_; }
    
    void reserve(size_type newCapacity) {
        if (newCapacity > capacity_) {
            reallocate(newCapacity);
        }
    }
    
    void shrinkToFit() {
        if (size_ < capacity_) {
            reallocate(size_);
        }
    }
    
    // Modifiers
    void clear() {
        for (size_type i = 0; i < size_; ++i) {
            data_[i].~T();
        }
        size_ = 0;
    }
    
    void resize(size_type newSize) {
        if (newSize > capacity_) {
            reserve(newSize);
        }
        
        if (newSize > size_) {
            for (size_type i = size_; i < newSize; ++i) {
                new (data_ + i) T();
            }
        } else if (newSize < size_) {
            for (size_type i = newSize; i < size_; ++i) {
                data_[i].~T();
            }
        }
        
        size_ = newSize;
    }
    
    void resize(size_type newSize, const T& value) {
        if (newSize > capacity_) {
            reserve(newSize);
        }
        
        if (newSize > size_) {
            for (size_type i = size_; i < newSize; ++i) {
                new (data_ + i) T(value);
            }
        } else if (newSize < size_) {
            for (size_type i = newSize; i < size_; ++i) {
                data_[i].~T();
            }
        }
        
        size_ = newSize;
    }
    
    void pushBack(const T& value) {
        if (size_ >= capacity_) {
            reserve(capacity_ == 0 ? 1 : capacity_ * 2);
        }
        new (data_ + size_) T(value);
        ++size_;
    }
    
    void pushBack(T&& value) {
        if (size_ >= capacity_) {
            reserve(capacity_ == 0 ? 1 : capacity_ * 2);
        }
        new (data_ + size_) T(std::move(value));
        ++size_;
    }
    
    template<typename... Args>
    reference emplaceBack(Args&&... args) {
        if (size_ >= capacity_) {
            reserve(capacity_ == 0 ? 1 : capacity_ * 2);
        }
        new (data_ + size_) T(std::forward<Args>(args)...);
        return data_[size_++];
    }
    
    void popBack() {
        if (size_ > 0) {
            data_[--size_].~T();
        }
    }

private:
    void reallocate(size_type newCapacity);
    void deallocate();

private:
    T* data_;
    size_type size_;
    size_type capacity_;
    IAllocator* allocator_;
};

} // namespace PlaytimeEngine
