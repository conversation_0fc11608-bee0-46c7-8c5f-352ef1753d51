#pragma once

/**
 * @file MemoryTracker.h
 * @brief Memory tracking and debugging utilities
 */

#include "../Types.h"
#include <unordered_map>
#include <mutex>

namespace PlaytimeEngine {

/**
 * @brief Memory allocation information for tracking
 */
struct AllocationInfo {
    size_t size;
    size_t alignment;
    const char* file;
    int line;
    const char* function;
    uint64 timestamp;
    uint32 allocationId;
};

/**
 * @brief Memory tracker for debugging memory leaks and usage
 */
class ENGINE_API MemoryTracker : public NonCopyable {
public:
    /**
     * @brief Memory statistics
     */
    struct Stats {
        size_t totalAllocated = 0;
        size_t totalFreed = 0;
        size_t currentUsage = 0;
        size_t peakUsage = 0;
        uint32 allocationCount = 0;
        uint32 freeCount = 0;
        uint32 leakCount = 0;
    };

    MemoryTracker();
    ~MemoryTracker();
    
    /**
     * @brief Record an allocation
     */
    void recordAllocation(void* ptr, size_t size, size_t alignment, 
                         const char* file, int line, const char* function);
    
    /**
     * @brief Record a deallocation
     */
    void recordDeallocation(void* ptr);
    
    /**
     * @brief Get memory statistics
     */
    Stats getStats() const;
    
    /**
     * @brief Reset statistics
     */
    void reset();
    
    /**
     * @brief Dump all current allocations
     */
    void dumpAllocations() const;
    
    /**
     * @brief Dump memory leaks
     */
    void dumpLeaks() const;
    
    /**
     * @brief Check for memory leaks
     */
    bool hasLeaks() const;
    
    /**
     * @brief Get allocation info for a pointer
     */
    const AllocationInfo* getAllocationInfo(void* ptr) const;
    
    /**
     * @brief Enable/disable tracking
     */
    void setEnabled(bool enabled) { enabled_ = enabled; }
    
    /**
     * @brief Check if tracking is enabled
     */
    bool isEnabled() const { return enabled_; }
    
    /**
     * @brief Set maximum number of tracked allocations
     */
    void setMaxAllocations(uint32 maxAllocations) { maxAllocations_ = maxAllocations; }
    
    /**
     * @brief Get maximum number of tracked allocations
     */
    uint32 getMaxAllocations() const { return maxAllocations_; }

private:
    /**
     * @brief Get current timestamp
     */
    uint64 getCurrentTimestamp() const;
    
    /**
     * @brief Generate unique allocation ID
     */
    uint32 generateAllocationId();

private:
    mutable std::mutex mutex_;
    std::unordered_map<void*, AllocationInfo> allocations_;
    Stats stats_;
    uint32 nextAllocationId_;
    uint32 maxAllocations_;
    bool enabled_;
};

/**
 * @brief Global memory tracking functions
 */
namespace MemoryTracking {
    /**
     * @brief Initialize global memory tracking
     */
    ENGINE_API bool initialize();
    
    /**
     * @brief Shutdown global memory tracking
     */
    ENGINE_API void shutdown();
    
    /**
     * @brief Get the global memory tracker
     */
    ENGINE_API MemoryTracker* getTracker();
    
    /**
     * @brief Record allocation with tracking info
     */
    ENGINE_API void recordAllocation(void* ptr, size_t size, size_t alignment,
                                    const char* file, int line, const char* function);
    
    /**
     * @brief Record deallocation
     */
    ENGINE_API void recordDeallocation(void* ptr);
    
    /**
     * @brief Enable/disable tracking
     */
    ENGINE_API void setEnabled(bool enabled);
    
    /**
     * @brief Check if tracking is enabled
     */
    ENGINE_API bool isEnabled();
}

} // namespace PlaytimeEngine

/**
 * @brief Memory tracking macros
 */
#if BUILD_DEBUG
    #define TRACK_ALLOCATION(ptr, size, alignment) \
        PlaytimeEngine::MemoryTracking::recordAllocation(ptr, size, alignment, __FILE__, __LINE__, __FUNCTION__)
    
    #define TRACK_DEALLOCATION(ptr) \
        PlaytimeEngine::MemoryTracking::recordDeallocation(ptr)
#else
    #define TRACK_ALLOCATION(ptr, size, alignment) ((void)0)
    #define TRACK_DEALLOCATION(ptr) ((void)0)
#endif
