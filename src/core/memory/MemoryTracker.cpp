
/**
 * @file MemoryTracker.cpp
 * @brief Memory tracking and leak detection implementation
 */

#include "../../include/core/memory/MemoryTracker.h"
#include <iostream>
#include <iomanip>

namespace PlaytimeEngine {
namespace Memory {

MemoryTracker::MemoryTracker()
    : totalAllocated_(0)
    , totalDeallocated_(0)
    , peakUsage_(0)
    , allocationCount_(0)
    , deallocationCount_(0) {
}

MemoryTracker::~MemoryTracker() {
    if (!allocations_.empty()) {
        reportLeaks();
    }
}

void MemoryTracker::recordAllocation(void* ptr, size_t size, const char* file, int line) {
    if (!ptr) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    AllocationInfo info;
    info.size = size;
    info.file = file ? file : "unknown";
    info.line = line;
    info.timestamp = std::chrono::high_resolution_clock::now();
    
    allocations_[ptr] = info;
    
    totalAllocated_ += size;
    allocationCount_++;
    
    size_t currentUsage = totalAllocated_ - totalDeallocated_;
    if (currentUsage > peakUsage_) {
        peakUsage_ = currentUsage;
    }
}

void MemoryTracker::recordDeallocation(void* ptr) {
    if (!ptr) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = allocations_.find(ptr);
    if (it != allocations_.end()) {
        totalDeallocated_ += it->second.size;
        deallocationCount_++;
        allocations_.erase(it);
    } else {
        // Double free or invalid pointer
        std::cerr << "WARNING: Attempting to free untracked pointer: " << ptr << std::endl;
    }
}

MemoryStats MemoryTracker::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    MemoryStats stats;
    stats.totalAllocated = totalAllocated_;
    stats.totalDeallocated = totalDeallocated_;
    stats.currentUsage = totalAllocated_ - totalDeallocated_;
    stats.peakUsage = peakUsage_;
    stats.allocationCount = allocationCount_;
    stats.deallocationCount = deallocationCount_;
    stats.activeAllocations = allocations_.size();
    
    return stats;
}

void MemoryTracker::reportLeaks() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (allocations_.empty()) {
        std::cout << "No memory leaks detected." << std::endl;
        return;
    }
    
    std::cout << "\n=== MEMORY LEAK REPORT ===" << std::endl;
    std::cout << "Total leaked allocations: " << allocations_.size() << std::endl;
    
    size_t totalLeaked = 0;
    for (const auto& pair : allocations_) {
        totalLeaked += pair.second.size;
    }
    
    std::cout << "Total leaked memory: " << totalLeaked << " bytes" << std::endl;
    std::cout << "\nLeak details:" << std::endl;
    
    for (const auto& pair : allocations_) {
        const auto& info = pair.second;
        std::cout << "  " << pair.first 
                  << " (" << info.size << " bytes) "
                  << "at " << info.file << ":" << info.line << std::endl;
    }
    
    std::cout << "=========================" << std::endl;
}

void MemoryTracker::reset() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    allocations_.clear();
    totalAllocated_ = 0;
    totalDeallocated_ = 0;
    peakUsage_ = 0;
    allocationCount_ = 0;
    deallocationCount_ = 0;
}

// Global memory tracker instance
static UniquePtr<MemoryTracker> g_memoryTracker;

namespace Tracking {

void initialize() {
    if (!g_memoryTracker) {
        g_memoryTracker = std::make_unique<MemoryTracker>();
    }
}

void shutdown() {
    if (g_memoryTracker) {
        g_memoryTracker->reportLeaks();
        g_memoryTracker.reset();
    }
}

MemoryTracker* getTracker() {
    return g_memoryTracker.get();
}

void recordAllocation(void* ptr, size_t size, const char* file, int line) {
    if (g_memoryTracker) {
        g_memoryTracker->recordAllocation(ptr, size, file, line);
    }
}

void recordDeallocation(void* ptr) {
    if (g_memoryTracker) {
        g_memoryTracker->recordDeallocation(ptr);
    }
}

} // namespace Tracking

} // namespace Memory
} // namespace PlaytimeEngine
