#pragma once

/**
 * @file LinearAllocator.h
 * @brief Linear allocator for frame-based allocations
 */

#include "IAllocator.h"
#include <mutex>

namespace PlaytimeEngine {

/**
 * @brief Linear allocator for fast sequential allocations
 * 
 * This allocator is perfect for frame-based allocations where
 * all memory is freed at once at the end of the frame.
 */
class ENGINE_API LinearAllocator : public IAllocator {
public:
    /**
     * @brief Constructor
     * @param size Total size of the linear buffer
     */
    explicit LinearAllocator(size_t size);
    
    /**
     * @brief Destructor
     */
    ~LinearAllocator();
    
    // IAllocator interface
    void* allocate(size_t size, size_t alignment = sizeof(void*)) override;
    void deallocate(void* ptr) override; // No-op for linear allocator
    
    size_t getTotalSize() const override { return totalSize_; }
    size_t getUsedSize() const override { return currentOffset_; }
    uint32 getAllocationCount() const override { return allocationCount_; }
    bool owns(void* ptr) const override;
    const char* getName() const override { return "LinearAllocator"; }
    
    Stats getStats() const override;
    void reset() override;
    void dump() const override;
    
    /**
     * @brief Get current offset
     */
    size_t getCurrentOffset() const { return currentOffset_; }
    
    /**
     * @brief Get remaining space
     */
    size_t getRemainingSpace() const { return totalSize_ - currentOffset_; }
    
    /**
     * @brief Check if allocator is full
     */
    bool isFull() const { return currentOffset_ >= totalSize_; }
    
    /**
     * @brief Check if allocator is empty
     */
    bool isEmpty() const { return currentOffset_ == 0; }
    
    /**
     * @brief Get memory utilization percentage
     */
    float getUtilization() const {
        return totalSize_ > 0 ? (float)currentOffset_ / (float)totalSize_ : 0.0f;
    }

private:
    /**
     * @brief Align offset to required alignment
     */
    size_t alignOffset(size_t offset, size_t alignment) const;

private:
    void* memory_;                 ///< Linear buffer memory
    size_t totalSize_;            ///< Total buffer size
    size_t currentOffset_;        ///< Current allocation offset
    uint32 allocationCount_;      ///< Number of allocations made
    mutable std::mutex mutex_;    ///< Thread safety
};

/**
 * @brief Double-ended linear allocator
 * 
 * Allows allocation from both ends of the buffer, useful for
 * separating different types of allocations.
 */
class ENGINE_API DoubleEndedLinearAllocator : public IAllocator {
public:
    /**
     * @brief Constructor
     * @param size Total size of the buffer
     */
    explicit DoubleEndedLinearAllocator(size_t size);
    
    /**
     * @brief Destructor
     */
    ~DoubleEndedLinearAllocator();
    
    // IAllocator interface
    void* allocate(size_t size, size_t alignment = sizeof(void*)) override;
    void deallocate(void* ptr) override; // No-op
    
    size_t getTotalSize() const override { return totalSize_; }
    size_t getUsedSize() const override { return frontOffset_ + (totalSize_ - backOffset_); }
    uint32 getAllocationCount() const override { return frontCount_ + backCount_; }
    bool owns(void* ptr) const override;
    const char* getName() const override { return "DoubleEndedLinearAllocator"; }
    
    Stats getStats() const override;
    void reset() override;
    void dump() const override;
    
    /**
     * @brief Allocate from the front of the buffer
     */
    void* allocateFront(size_t size, size_t alignment = sizeof(void*));
    
    /**
     * @brief Allocate from the back of the buffer
     */
    void* allocateBack(size_t size, size_t alignment = sizeof(void*));
    
    /**
     * @brief Get front offset
     */
    size_t getFrontOffset() const { return frontOffset_; }
    
    /**
     * @brief Get back offset
     */
    size_t getBackOffset() const { return backOffset_; }
    
    /**
     * @brief Get remaining space
     */
    size_t getRemainingSpace() const {
        return backOffset_ > frontOffset_ ? backOffset_ - frontOffset_ : 0;
    }
    
    /**
     * @brief Check if allocators have collided
     */
    bool hasCollided() const { return frontOffset_ >= backOffset_; }

private:
    /**
     * @brief Align offset to required alignment
     */
    size_t alignOffsetUp(size_t offset, size_t alignment) const;
    size_t alignOffsetDown(size_t offset, size_t alignment) const;

private:
    void* memory_;                 ///< Buffer memory
    size_t totalSize_;            ///< Total buffer size
    size_t frontOffset_;          ///< Front allocation offset
    size_t backOffset_;           ///< Back allocation offset
    uint32 frontCount_;           ///< Front allocation count
    uint32 backCount_;            ///< Back allocation count
    mutable std::mutex mutex_;    ///< Thread safety
};

} // namespace PlaytimeEngine
