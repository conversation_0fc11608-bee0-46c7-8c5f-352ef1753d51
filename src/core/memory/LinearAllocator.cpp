
/**
 * @file LinearAllocator.cpp
 * @brief Linear allocator implementation
 */

#include "../../include/core/memory/LinearAllocator.h"
#include <cstdlib>
#include <cassert>

namespace PlaytimeEngine {
namespace Memory {

LinearAllocator::LinearAllocator(size_t size)
    : size_(size)
    , used_(0)
    , memory_(nullptr) {
}

LinearAllocator::~LinearAllocator() {
    if (memory_) {
        std::free(memory_);
    }
}

bool LinearAllocator::initialize() {
    if (memory_) {
        return false; // Already initialized
    }
    
    memory_ = std::malloc(size_);
    if (!memory_) {
        return false;
    }
    
    used_ = 0;
    return true;
}

void* LinearAllocator::allocate(size_t size, size_t alignment) {
    if (!memory_ || size == 0) {
        return nullptr;
    }
    
    // Align the current position
    size_t currentPos = reinterpret_cast<uintptr_t>(static_cast<char*>(memory_) + used_);
    size_t alignedPos = (currentPos + alignment - 1) & ~(alignment - 1);
    size_t padding = alignedPos - currentPos;
    
    if (used_ + padding + size > size_) {
        return nullptr; // Out of memory
    }
    
    void* result = static_cast<char*>(memory_) + used_ + padding;
    used_ += padding + size;
    
    return result;
}

void LinearAllocator::deallocate(void* ptr) {
    // Linear allocators don't support individual deallocation
    // This is intentionally a no-op
}

void LinearAllocator::reset() {
    used_ = 0;
}

size_t LinearAllocator::getUsedMemory() const {
    return used_;
}

size_t LinearAllocator::getTotalMemory() const {
    return size_;
}

size_t LinearAllocator::getAvailableMemory() const {
    return size_ - used_;
}

} // namespace Memory
} // namespace PlaytimeEngine
