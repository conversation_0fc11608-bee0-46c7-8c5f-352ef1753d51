
/**
 * @file StackAllocator.cpp
 * @brief Stack allocator implementation
 */

#include "../../include/core/memory/StackAllocator.h"
#include <cstdlib>
#include <cassert>

namespace PlaytimeEngine {
namespace Memory {

StackAllocator::StackAllocator(size_t size)
    : size_(size)
    , top_(0)
    , memory_(nullptr) {
}

StackAllocator::~StackAllocator() {
    if (memory_) {
        std::free(memory_);
    }
}

bool StackAllocator::initialize() {
    if (memory_) {
        return false; // Already initialized
    }
    
    memory_ = std::malloc(size_);
    if (!memory_) {
        return false;
    }
    
    top_ = 0;
    return true;
}

void* StackAllocator::allocate(size_t size, size_t alignment) {
    if (!memory_ || size == 0) {
        return nullptr;
    }
    
    // Align the current position
    size_t currentPos = reinterpret_cast<uintptr_t>(static_cast<char*>(memory_) + top_);
    size_t alignedPos = (currentPos + alignment - 1) & ~(alignment - 1);
    size_t padding = alignedPos - currentPos;
    
    if (top_ + padding + size > size_) {
        return nullptr; // Out of memory
    }
    
    void* result = static_cast<char*>(memory_) + top_ + padding;
    top_ += padding + size;
    
    return result;
}

void StackAllocator::deallocate(void* ptr) {
    // Stack allocators support deallocation only in LIFO order
    // For simplicity, this is a placeholder implementation
}

StackAllocator::Marker StackAllocator::getMarker() const {
    return top_;
}

void StackAllocator::freeToMarker(Marker marker) {
    if (marker <= size_) {
        top_ = marker;
    }
}

void StackAllocator::clear() {
    top_ = 0;
}

size_t StackAllocator::getUsedMemory() const {
    return top_;
}

size_t StackAllocator::getTotalMemory() const {
    return size_;
}

size_t StackAllocator::getAvailableMemory() const {
    return size_ - top_;
}

} // namespace Memory
} // namespace PlaytimeEngine
