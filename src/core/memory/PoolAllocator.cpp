
/**
 * @file PoolAllocator.cpp
 * @brief Pool allocator implementation
 */

#include "../../include/core/memory/PoolAllocator.h"
#include <cstdlib>
#include <cassert>

namespace PlaytimeEngine {
namespace Memory {

PoolAllocator::PoolAllocator(size_t objectSize, size_t objectCount)
    : objectSize_(objectSize)
    , objectCount_(objectCount)
    , freeHead_(nullptr)
    , memory_(nullptr) {
    // Ensure object size is at least as large as a pointer
    if (objectSize_ < sizeof(void*)) {
        objectSize_ = sizeof(void*);
    }
}

PoolAllocator::~PoolAllocator() {
    if (memory_) {
        std::free(memory_);
    }
}

bool PoolAllocator::initialize() {
    if (memory_) {
        return false; // Already initialized
    }
    
    size_t totalSize = objectSize_ * objectCount_;
    memory_ = std::malloc(totalSize);
    if (!memory_) {
        return false;
    }
    
    // Initialize free list
    char* current = static_cast<char*>(memory_);
    freeHead_ = current;
    
    for (size_t i = 0; i < objectCount_ - 1; ++i) {
        void** next = reinterpret_cast<void**>(current);
        current += objectSize_;
        *next = current;
    }
    
    // Last object points to nullptr
    void** last = reinterpret_cast<void**>(current);
    *last = nullptr;
    
    return true;
}

void* PoolAllocator::allocate(size_t size, size_t alignment) {
    if (!memory_ || size > objectSize_ || !freeHead_) {
        return nullptr;
    }
    
    void* result = freeHead_;
    freeHead_ = *static_cast<void**>(freeHead_);
    
    return result;
}

void PoolAllocator::deallocate(void* ptr) {
    if (!ptr || !memory_) {
        return;
    }
    
    // Add back to free list
    void** node = static_cast<void**>(ptr);
    *node = freeHead_;
    freeHead_ = ptr;
}

size_t PoolAllocator::getObjectSize() const {
    return objectSize_;
}

size_t PoolAllocator::getObjectCount() const {
    return objectCount_;
}

size_t PoolAllocator::getUsedMemory() const {
    // Count free objects
    size_t freeCount = 0;
    void* current = freeHead_;
    while (current) {
        ++freeCount;
        current = *static_cast<void**>(current);
    }
    
    size_t usedCount = objectCount_ - freeCount;
    return usedCount * objectSize_;
}

size_t PoolAllocator::getTotalMemory() const {
    return objectSize_ * objectCount_;
}

size_t PoolAllocator::getAvailableMemory() const {
    return getTotalMemory() - getUsedMemory();
}

} // namespace Memory
} // namespace PlaytimeEngine
