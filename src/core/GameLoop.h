#pragma once

/**
 * @file GameLoop.h
 * @brief Main game loop implementation with fixed timestep
 */

#include "Types.h"
#include <functional>

namespace PlaytimeEngine {

/**
 * @brief Game loop timing statistics
 */
struct LoopStats {
    float deltaTime = 0.0f;
    float frameTime = 0.0f;
    float fps = 0.0f;
    uint64 frameNumber = 0;
    double totalTime = 0.0;
    
    // Performance metrics
    float minFrameTime = 0.0f;
    float maxFrameTime = 0.0f;
    float avgFrameTime = 0.0f;
    uint32 frameCount = 0;
};

/**
 * @brief Main game loop manager
 */
class ENGINE_API GameLoop : public NonCopyable {
public:
    /**
     * @brief Game loop mode
     */
    enum class Mode {
        VariableTimestep,   ///< Variable timestep (default)
        FixedTimestep,      ///< Fixed timestep with accumulation
        SemiFixed          ///< Semi-fixed timestep with maximum delta
    };

    GameLoop();
    ~GameLoop();
    
    /**
     * @brief Initialize the game loop
     */
    bool initialize();
    
    /**
     * @brief Run one frame of the game loop
     * @return True to continue, false to exit
     */
    bool tick();
    
    /**
     * @brief Shutdown the game loop
     */
    void shutdown();
    
    /**
     * @brief Set the target frame rate
     */
    void setTargetFrameRate(uint32 fps);
    
    /**
     * @brief Get the target frame rate
     */
    uint32 getTargetFrameRate() const { return targetFPS_; }
    
    /**
     * @brief Set the game loop mode
     */
    void setMode(Mode mode) { mode_ = mode; }
    
    /**
     * @brief Get the game loop mode
     */
    Mode getMode() const { return mode_; }
    
    /**
     * @brief Set fixed timestep (for fixed mode)
     */
    void setFixedTimestep(float timestep) { fixedTimestep_ = timestep; }
    
    /**
     * @brief Get fixed timestep
     */
    float getFixedTimestep() const { return fixedTimestep_; }
    
    /**
     * @brief Set maximum delta time (for semi-fixed mode)
     */
    void setMaxDeltaTime(float maxDelta) { maxDeltaTime_ = maxDelta; }
    
    /**
     * @brief Get maximum delta time
     */
    float getMaxDeltaTime() const { return maxDeltaTime_; }
    
    /**
     * @brief Get current loop statistics
     */
    const LoopStats& getStats() const { return stats_; }
    
    /**
     * @brief Reset performance statistics
     */
    void resetStats();
    
    /**
     * @brief Set update callback
     */
    void setUpdateCallback(std::function<void(float)> callback) {
        updateCallback_ = callback;
    }
    
    /**
     * @brief Set render callback
     */
    void setRenderCallback(std::function<void()> callback) {
        renderCallback_ = callback;
    }
    
    /**
     * @brief Request loop exit
     */
    void requestExit() { exitRequested_ = true; }
    
    /**
     * @brief Check if exit was requested
     */
    bool isExitRequested() const { return exitRequested_; }
    
    /**
     * @brief Pause the game loop
     */
    void pause() { paused_ = true; }
    
    /**
     * @brief Resume the game loop
     */
    void resume() { paused_ = false; }
    
    /**
     * @brief Check if the game loop is paused
     */
    bool isPaused() const { return paused_; }

private:
    /**
     * @brief Update timing statistics
     */
    void updateStats(float frameTime);
    
    /**
     * @brief Sleep to maintain target frame rate
     */
    void maintainFrameRate();
    
    /**
     * @brief Get high-resolution time
     */
    double getCurrentTime() const;

private:
    Mode mode_;
    uint32 targetFPS_;
    float fixedTimestep_;
    float maxDeltaTime_;
    
    LoopStats stats_;
    
    double lastFrameTime_;
    double accumulator_;
    bool initialized_;
    bool exitRequested_;
    bool paused_;
    
    // Callbacks
    std::function<void(float)> updateCallback_;
    std::function<void()> renderCallback_;
    
    // Performance tracking
    static constexpr uint32 FRAME_HISTORY_SIZE = 60;
    float frameHistory_[FRAME_HISTORY_SIZE];
    uint32 frameHistoryIndex_;
};

} // namespace PlaytimeEngine
