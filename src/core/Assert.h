#pragma once

/**
 * @file Assert.h
 * @brief Assertion macros for debugging
 */

#include "Types.h"
#include "Platform.h"

namespace PlaytimeEngine {

/**
 * @brief Assert handler function type
 */
using AssertHandler = void(*)(const char* condition, const char* message, const char* file, int line);

/**
 * @brief Set custom assert handler
 */
ENGINE_API void setAssertHandler(AssertHandler handler);

/**
 * @brief Default assert handler
 */
ENGINE_API void defaultAssertHandler(const char* condition, const char* message, const char* file, int line);

/**
 * @brief Trigger an assertion
 */
ENGINE_API void triggerAssert(const char* condition, const char* message, const char* file, int line);

} // namespace PlaytimeEngine

// Assert macros
#if BUILD_DEBUG
    #define ASSERT(condition) \
        do { \
            if (!(condition)) { \
                PlaytimeEngine::triggerAssert(#condition, nullptr, __FILE__, __LINE__); \
            } \
        } while (0)

    #define ASSERT_MSG(condition, message) \
        do { \
            if (!(condition)) { \
                PlaytimeEngine::triggerAssert(#condition, message, __FILE__, __LINE__); \
            } \
        } while (0)

    #define ASSERT_ALWAYS(message) \
        do { \
            PlaytimeEngine::triggerAssert("false", message, __FILE__, __LINE__); \
        } while (0)

    #define VERIFY(condition) ASSERT(condition)
    #define VERIFY_MSG(condition, message) ASSERT_MSG(condition, message)

#else
    #define ASSERT(condition) ((void)0)
    #define ASSERT_MSG(condition, message) ((void)0)
    #define ASSERT_ALWAYS(message) ((void)0)
    #define VERIFY(condition) ((void)(condition))
    #define VERIFY_MSG(condition, message) ((void)(condition))
#endif

// Static assert for compile-time checks
#define STATIC_ASSERT(condition, message) static_assert(condition, message)
