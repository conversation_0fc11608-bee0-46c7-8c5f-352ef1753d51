#pragma once

/**
 * @file SubsystemManager.h
 * @brief Subsystem management and dependency injection
 */

#include "Types.h"
#include <unordered_map>
#include <vector>
#include <type_traits>

namespace PlaytimeEngine {

// Compile-time type hash for RTTI-free subsystem identification
template<typename T>
constexpr size_t getTypeHash() {
    return std::hash<const char*>{}(__PRETTY_FUNCTION__);
}

using SubsystemTypeID = size_t;

/**
 * @brief Base class for all engine subsystems
 */
class ENGINE_API ISubsystem : public NonCopyable {
public:
    virtual ~ISubsystem() = default;
    
    /**
     * @brief Initialize the subsystem
     */
    virtual bool initialize() = 0;
    
    /**
     * @brief Update the subsystem
     */
    virtual void update(float deltaTime) = 0;
    
    /**
     * @brief Shutdown the subsystem
     */
    virtual void shutdown() = 0;
    
    /**
     * @brief Get subsystem name
     */
    virtual const char* getName() const = 0;
    
    /**
     * @brief Get subsystem dependencies
     */
    virtual std::vector<SubsystemTypeID> getDependencies() const { return {}; }
};

/**
 * @brief Manages all engine subsystems with dependency injection
 */
class ENGINE_API SubsystemManager : public NonCopyable {
public:
    SubsystemManager();
    ~SubsystemManager();
    
    /**
     * @brief Register a subsystem
     */
    template<typename T>
    void registerSubsystem(UniquePtr<T> subsystem) {
        static_assert(std::is_base_of_v<ISubsystem, T>, "T must inherit from ISubsystem");
        
        SubsystemTypeID typeId = getTypeHash<T>();
        subsystems_[typeId] = std::move(subsystem);
        initializationOrder_.push_back(typeId);
    }
    
    /**
     * @brief Get a subsystem by type
     */
    template<typename T>
    T* getSubsystem() {
        static_assert(std::is_base_of_v<ISubsystem, T>, "T must inherit from ISubsystem");
        
        SubsystemTypeID typeId = getTypeHash<T>();
        auto it = subsystems_.find(typeId);
        if (it != subsystems_.end()) {
            return static_cast<T*>(it->second.get());
        }
        return nullptr;
    }
    
    /**
     * @brief Initialize all subsystems in dependency order
     */
    bool initializeAll();
    
    /**
     * @brief Update all subsystems
     */
    void updateAll(float deltaTime);
    
    /**
     * @brief Shutdown all subsystems in reverse order
     */
    void shutdownAll();
    
    /**
     * @brief Check if a subsystem is registered
     */
    template<typename T>
    bool hasSubsystem() const {
        SubsystemTypeID typeId = getTypeHash<T>();
        return subsystems_.find(typeId) != subsystems_.end();
    }
    
    /**
     * @brief Get number of registered subsystems
     */
    size_t getSubsystemCount() const { return subsystems_.size(); }

private:
    /**
     * @brief Resolve subsystem dependencies
     */
    bool resolveDependencies();
    
    /**
     * @brief Topological sort for dependency resolution
     */
    std::vector<SubsystemTypeID> topologicalSort();

private:
    std::unordered_map<SubsystemTypeID, UniquePtr<ISubsystem>> subsystems_;
    std::vector<SubsystemTypeID> initializationOrder_;
    bool initialized_;
};

} // namespace PlaytimeEngine
