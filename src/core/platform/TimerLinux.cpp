
/**
 * @file TimerLinux.cpp
 * @brief Linux-specific timer implementation
 */

#include "../../include/core/platform/Timer.h"
#include <time.h>

namespace PlaytimeEngine {
namespace Platform {

uint64 Timer::getCurrentTimeNanoseconds() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return static_cast<uint64>(ts.tv_sec) * 1000000000ULL + static_cast<uint64>(ts.tv_nsec);
}

} // namespace Platform
} // namespace PlaytimeEngine
