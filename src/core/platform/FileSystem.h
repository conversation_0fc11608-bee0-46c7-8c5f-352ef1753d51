#pragma once

/**
 * @file FileSystem.h
 * @brief Cross-platform file system operations
 */

#include "../Types.h"
#include <vector>

namespace PlaytimeEngine {
namespace FileSystem {

/**
 * @brief File attributes
 */
struct FileInfo {
    String name;
    String fullPath;
    uint64 size;
    uint64 lastModified;
    bool isDirectory;
    bool isReadOnly;
    bool isHidden;
};

/**
 * @brief Check if a file exists
 */
ENGINE_API bool exists(const String& path);

/**
 * @brief Check if path is a directory
 */
ENGINE_API bool isDirectory(const String& path);

/**
 * @brief Check if path is a file
 */
ENGINE_API bool isFile(const String& path);

/**
 * @brief Get file size
 */
ENGINE_API uint64 getFileSize(const String& path);

/**
 * @brief Get file modification time
 */
ENGINE_API uint64 getLastModified(const String& path);

/**
 * @brief Create directory
 */
ENGINE_API bool createDirectory(const String& path);

/**
 * @brief Create directories recursively
 */
ENGINE_API bool createDirectories(const String& path);

/**
 * @brief Delete file
 */
ENGINE_API bool deleteFile(const String& path);

/**
 * @brief Delete directory
 */
ENGINE_API bool deleteDirectory(const String& path, bool recursive = false);

/**
 * @brief Copy file
 */
ENGINE_API bool copyFile(const String& source, const String& destination);

/**
 * @brief Move/rename file
 */
ENGINE_API bool moveFile(const String& source, const String& destination);

/**
 * @brief List directory contents
 */
ENGINE_API std::vector<FileInfo> listDirectory(const String& path);

/**
 * @brief Read entire file as string
 */
ENGINE_API String readTextFile(const String& path);

/**
 * @brief Read entire file as binary data
 */
ENGINE_API std::vector<uint8> readBinaryFile(const String& path);

/**
 * @brief Write string to file
 */
ENGINE_API bool writeTextFile(const String& path, const String& content);

/**
 * @brief Write binary data to file
 */
ENGINE_API bool writeBinaryFile(const String& path, const std::vector<uint8>& data);

/**
 * @brief Append string to file
 */
ENGINE_API bool appendTextFile(const String& path, const String& content);

} // namespace FileSystem
} // namespace PlaytimeEngine
