/**
 * @file PlatformLinux.cpp
 * @brief Linux-specific platform implementations
 */

#include "Platform.h"
#include "String.h"

#if PLATFORM_LINUX

#include <unistd.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <pthread.h>
#include <sched.h>
#include <errno.h>
#include <cstdlib>
#include <algorithm>

namespace PlaytimeEngine {
namespace Platform {

String normalizePath(const String& path) {
    if (path.empty()) {
        return path;
    }
    
    String result = path;
    
    // Replace backslashes with forward slashes
    std::replace(result.begin(), result.end(), '\\', '/');
    
    // Remove duplicate slashes
    size_t pos = 0;
    while ((pos = result.find("//", pos)) != String::npos) {
        result.erase(pos, 1);
    }
    
    // Remove trailing slash (except for root)
    if (result.length() > 1 && result.back() == '/') {
        result.pop_back();
    }
    
    return result;
}

bool isAbsolutePath(const String& path) {
    return !path.empty() && path[0] == '/';
}

String combinePaths(const String& basePath, const String& relativePath) {
    if (basePath.empty()) {
        return normalizePath(relativePath);
    }
    
    if (relativePath.empty()) {
        return normalizePath(basePath);
    }
    
    if (isAbsolutePath(relativePath)) {
        return normalizePath(relativePath);
    }
    
    String result = basePath;
    if (result.back() != '/') {
        result += '/';
    }
    result += relativePath;
    
    return normalizePath(result);
}

String getParentDirectory(const String& path) {
    if (path.empty()) {
        return "";
    }
    
    String normalized = normalizePath(path);
    size_t lastSlash = normalized.find_last_of('/');
    
    if (lastSlash == String::npos) {
        return ".";
    }
    
    if (lastSlash == 0) {
        return "/";
    }
    
    return normalized.substr(0, lastSlash);
}

String getFileName(const String& path) {
    if (path.empty()) {
        return "";
    }
    
    String normalized = normalizePath(path);
    size_t lastSlash = normalized.find_last_of('/');
    
    if (lastSlash == String::npos) {
        return normalized;
    }
    
    return normalized.substr(lastSlash + 1);
}

String getFileExtension(const String& path) {
    String filename = getFileName(path);
    size_t lastDot = filename.find_last_of('.');
    
    if (lastDot == String::npos || lastDot == 0) {
        return "";
    }
    
    return filename.substr(lastDot);
}

void* alignedAlloc(size_t size, size_t alignment) {
    void* ptr = nullptr;
    
    // alignment must be a power of 2 and at least sizeof(void*)
    if (alignment < sizeof(void*)) {
        alignment = sizeof(void*);
    }
    
    int result = posix_memalign(&ptr, alignment, size);
    if (result != 0) {
        return nullptr;
    }
    
    return ptr;
}

void alignedFree(void* ptr) {
    if (ptr) {
        free(ptr);
    }
}

void* virtualAlloc(size_t size, bool commit) {
    int flags = MAP_PRIVATE | MAP_ANONYMOUS;
    int prot = PROT_NONE;
    
    if (commit) {
        prot = PROT_READ | PROT_WRITE;
    }
    
    void* ptr = mmap(nullptr, size, prot, flags, -1, 0);
    if (ptr == MAP_FAILED) {
        return nullptr;
    }
    
    return ptr;
}

void virtualFree(void* ptr, size_t size) {
    if (ptr) {
        munmap(ptr, size);
    }
}

bool virtualCommit(void* ptr, size_t size) {
    if (!ptr) {
        return false;
    }
    
    return mprotect(ptr, size, PROT_READ | PROT_WRITE) == 0;
}

bool virtualDecommit(void* ptr, size_t size) {
    if (!ptr) {
        return false;
    }
    
    // Decommit by changing protection to PROT_NONE
    if (mprotect(ptr, size, PROT_NONE) != 0) {
        return false;
    }
    
    // Advise kernel that we don't need this memory
    madvise(ptr, size, MADV_DONTNEED);
    
    return true;
}

size_t getPageSize() {
    static size_t pageSize = 0;
    if (pageSize == 0) {
        pageSize = static_cast<size_t>(sysconf(_SC_PAGESIZE));
    }
    return pageSize;
}

size_t getAllocationGranularity() {
    // On Linux, allocation granularity is the same as page size
    return getPageSize();
}

bool setThreadAffinity(uint32 threadId, uint64 coreMask) {
    pthread_t thread = (threadId == 0) ? pthread_self() : static_cast<pthread_t>(threadId);
    
    cpu_set_t cpuSet;
    CPU_ZERO(&cpuSet);
    
    // Set CPU affinity based on core mask
    for (int i = 0; i < 64; ++i) {
        if (coreMask & (1ULL << i)) {
            CPU_SET(i, &cpuSet);
        }
    }
    
    return pthread_setaffinity_np(thread, sizeof(cpu_set_t), &cpuSet) == 0;
}

} // namespace Platform
} // namespace PlaytimeEngine

#endif // PLATFORM_LINUX
