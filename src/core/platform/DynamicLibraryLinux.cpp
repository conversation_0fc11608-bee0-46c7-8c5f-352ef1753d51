
/**
 * @file DynamicLibraryLinux.cpp
 * @brief Linux-specific dynamic library implementation
 */

#include "../../include/core/platform/DynamicLibrary.h"
#include <dlfcn.h>

namespace PlaytimeEngine {
namespace Platform {

bool DynamicLibrary::load(const String& path) {
    if (handle_) {
        unload();
    }
    
    handle_ = dlopen(path.c_str(), RTLD_LAZY);
    return handle_ != nullptr;
}

void DynamicLibrary::unload() {
    if (handle_) {
        dlclose(handle_);
        handle_ = nullptr;
    }
}

void* DynamicLibrary::getSymbol(const String& symbolName) {
    if (!handle_) {
        return nullptr;
    }
    
    return dlsym(handle_, symbolName.c_str());
}

String DynamicLibrary::getLastError() const {
    const char* error = dlerror();
    return error ? String(error) : String();
}

} // namespace Platform
} // namespace PlaytimeEngine
