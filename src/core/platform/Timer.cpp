
/**
 * @file Timer.cpp
 * @brief Cross-platform timer implementation
 */

#include "../../include/core/platform/Timer.h"
#include <chrono>

namespace PlaytimeEngine {
namespace Platform {

Timer::Timer()
    : startTime_(0)
    , running_(false) {
}

void Timer::start() {
    startTime_ = getCurrentTimeNanoseconds();
    running_ = true;
}

void Timer::stop() {
    running_ = false;
}

void Timer::reset() {
    startTime_ = getCurrentTimeNanoseconds();
}

uint64 Timer::getElapsedNanoseconds() const {
    if (!running_) {
        return 0;
    }
    
    return getCurrentTimeNanoseconds() - startTime_;
}

uint64 Timer::getElapsedMicroseconds() const {
    return getElapsedNanoseconds() / 1000;
}

uint64 Timer::getElapsedMilliseconds() const {
    return getElapsedNanoseconds() / 1000000;
}

float64 Timer::getElapsedSeconds() const {
    return static_cast<float64>(getElapsedNanoseconds()) / 1000000000.0;
}

bool Timer::isRunning() const {
    return running_;
}

uint64 Timer::getCurrentTimeNanoseconds() {
    auto now = std::chrono::high_resolution_clock::now();
    return static_cast<uint64>(std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count());
}

} // namespace Platform
} // namespace PlaytimeEngine
