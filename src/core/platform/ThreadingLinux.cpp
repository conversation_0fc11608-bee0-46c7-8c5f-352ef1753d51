
/**
 * @file ThreadingLinux.cpp
 * @brief Linux-specific threading implementation
 */

#include "../../include/core/platform/Threading.h"
#include <pthread.h>
#include <unistd.h>
#include <sys/syscall.h>

namespace PlaytimeEngine {
namespace Platform {
namespace Threading {

uint32 getCurrentThreadId() {
    return static_cast<uint32>(syscall(SYS_gettid));
}

void setThreadName(const String& name) {
    pthread_setname_np(pthread_self(), name.c_str());
}

void setThreadPriority(ThreadPriority priority) {
    int policy = SCHED_OTHER;
    struct sched_param param;
    
    switch (priority) {
        case ThreadPriority::Low:
            param.sched_priority = 0;
            break;
        case ThreadPriority::Normal:
            param.sched_priority = 0;
            break;
        case ThreadPriority::High:
            param.sched_priority = 1;
            policy = SCHED_FIFO;
            break;
        case ThreadPriority::Critical:
            param.sched_priority = 99;
            policy = SCHED_FIFO;
            break;
    }
    
    pthread_setschedparam(pthread_self(), policy, &param);
}

void setThreadAffinity(uint64 affinityMask) {
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    
    for (int i = 0; i < 64; ++i) {
        if (affinityMask & (1ULL << i)) {
            CPU_SET(i, &cpuset);
        }
    }
    
    pthread_setaffinity_np(pthread_self(), sizeof(cpuset), &cpuset);
}

} // namespace Threading
} // namespace Platform
} // namespace PlaytimeEngine
