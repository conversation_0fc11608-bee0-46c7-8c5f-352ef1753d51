/**
 * @file Platform.cpp
 * @brief Cross-platform abstraction layer implementation
 */

#include "Platform.h"
#include "String.h"
#include <thread>
#include <chrono>

#if PLATFORM_LINUX
    #include <unistd.h>
    #include <sys/sysinfo.h>
    #include <sys/utsname.h>
    #include <sys/mman.h>
    #include <pthread.h>
    #include <climits>
    #include <cstdlib>
    #include <fstream>
    #include <iostream>
#elif PLATFORM_WINDOWS
    #include <windows.h>
    #include <psapi.h>
    #include <shlobj.h>
#endif

namespace PlaytimeEngine {
namespace Platform {

static bool s_initialized = false;
static PlatformInfo s_platformInfo;

InitResult initialize() {
    if (s_initialized) {
        return InitResult::Success;
    }

#if PLATFORM_LINUX
    // Get platform information
    s_platformInfo.platformName = "Linux";
    
    // Get OS version
    struct utsname unameData;
    if (uname(&unameData) == 0) {
        s_platformInfo.osVersion = String(unameData.sysname) + " " + 
                                  String(unameData.release) + " " + 
                                  String(unameData.version);
        s_platformInfo.cpuArchitecture = String(unameData.machine);
    } else {
        s_platformInfo.osVersion = "Unknown Linux";
        s_platformInfo.cpuArchitecture = "Unknown";
    }
    
    // Get CPU core count
    s_platformInfo.cpuCoreCount = std::thread::hardware_concurrency();
    if (s_platformInfo.cpuCoreCount == 0) {
        s_platformInfo.cpuCoreCount = 1;
    }
    
    // Get memory information
    struct sysinfo si;
    if (sysinfo(&si) == 0) {
        s_platformInfo.totalMemory = si.totalram * si.mem_unit;
        s_platformInfo.availableMemory = si.freeram * si.mem_unit;
    } else {
        s_platformInfo.totalMemory = 0;
        s_platformInfo.availableMemory = 0;
    }
    
    // Check for debugger (simplified check)
    s_platformInfo.isDebuggerPresent = false;
    std::ifstream statusFile("/proc/self/status");
    if (statusFile.is_open()) {
        String line;
        while (std::getline(statusFile, line)) {
            if (line.find("TracerPid:") == 0) {
                String pidStr = line.substr(10);
                int tracerPid = std::stoi(StringUtils::trim(pidStr));
                s_platformInfo.isDebuggerPresent = (tracerPid != 0);
                break;
            }
        }
    }
    
#elif PLATFORM_WINDOWS
    s_platformInfo.platformName = "Windows";
    
    // Get OS version
    OSVERSIONINFOEX osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOEX));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
    
    if (GetVersionEx((OSVERSIONINFO*)&osvi)) {
        s_platformInfo.osVersion = StringUtils::format("Windows %d.%d Build %d", 
                                                       osvi.dwMajorVersion, 
                                                       osvi.dwMinorVersion, 
                                                       osvi.dwBuildNumber);
    } else {
        s_platformInfo.osVersion = "Unknown Windows";
    }
    
    // Get CPU architecture
    SYSTEM_INFO si;
    GetSystemInfo(&si);
    
    switch (si.wProcessorArchitecture) {
        case PROCESSOR_ARCHITECTURE_AMD64:
            s_platformInfo.cpuArchitecture = "x64";
            break;
        case PROCESSOR_ARCHITECTURE_INTEL:
            s_platformInfo.cpuArchitecture = "x86";
            break;
        case PROCESSOR_ARCHITECTURE_ARM64:
            s_platformInfo.cpuArchitecture = "ARM64";
            break;
        default:
            s_platformInfo.cpuArchitecture = "Unknown";
            break;
    }
    
    s_platformInfo.cpuCoreCount = si.dwNumberOfProcessors;
    
    // Get memory information
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    if (GlobalMemoryStatusEx(&memStatus)) {
        s_platformInfo.totalMemory = memStatus.ullTotalPhys;
        s_platformInfo.availableMemory = memStatus.ullAvailPhys;
    } else {
        s_platformInfo.totalMemory = 0;
        s_platformInfo.availableMemory = 0;
    }
    
    s_platformInfo.isDebuggerPresent = IsDebuggerPresent() != FALSE;
    
#else
    return InitResult::UnsupportedPlatform;
#endif

    s_initialized = true;
    return InitResult::Success;
}

void shutdown() {
    s_initialized = false;
}

PlatformInfo getPlatformInfo() {
    return s_platformInfo;
}

String getCurrentDirectory() {
#if PLATFORM_LINUX
    char buffer[PATH_MAX];
    if (getcwd(buffer, sizeof(buffer)) != nullptr) {
        return String(buffer);
    }
    return "";
#elif PLATFORM_WINDOWS
    DWORD length = GetCurrentDirectoryA(0, nullptr);
    if (length > 0) {
        std::vector<char> buffer(length);
        if (GetCurrentDirectoryA(length, buffer.data()) > 0) {
            return String(buffer.data());
        }
    }
    return "";
#endif
}

bool setCurrentDirectory(const String& path) {
#if PLATFORM_LINUX
    return chdir(path.c_str()) == 0;
#elif PLATFORM_WINDOWS
    return SetCurrentDirectoryA(path.c_str()) != FALSE;
#endif
}

String getExecutableDirectory() {
#if PLATFORM_LINUX
    char buffer[PATH_MAX];
    ssize_t len = readlink("/proc/self/exe", buffer, sizeof(buffer) - 1);
    if (len != -1) {
        buffer[len] = '\0';
        String path(buffer);
        size_t lastSlash = path.find_last_of('/');
        if (lastSlash != String::npos) {
            return path.substr(0, lastSlash);
        }
    }
    return "";
#elif PLATFORM_WINDOWS
    char buffer[MAX_PATH];
    if (GetModuleFileNameA(nullptr, buffer, MAX_PATH) > 0) {
        String path(buffer);
        size_t lastSlash = path.find_last_of("\\/");
        if (lastSlash != String::npos) {
            return path.substr(0, lastSlash);
        }
    }
    return "";
#endif
}

String getDocumentsDirectory() {
#if PLATFORM_LINUX
    const char* home = getenv("HOME");
    if (home) {
        return String(home) + "/Documents";
    }
    return "";
#elif PLATFORM_WINDOWS
    char buffer[MAX_PATH];
    if (SHGetFolderPathA(nullptr, CSIDL_MYDOCUMENTS, nullptr, SHGFP_TYPE_CURRENT, buffer) == S_OK) {
        return String(buffer);
    }
    return "";
#endif
}

String getAppDataDirectory() {
#if PLATFORM_LINUX
    const char* home = getenv("HOME");
    if (home) {
        return String(home) + "/.local/share";
    }
    return "";
#elif PLATFORM_WINDOWS
    char buffer[MAX_PATH];
    if (SHGetFolderPathA(nullptr, CSIDL_APPDATA, nullptr, SHGFP_TYPE_CURRENT, buffer) == S_OK) {
        return String(buffer);
    }
    return "";
#endif
}

String getTempDirectory() {
#if PLATFORM_LINUX
    const char* tmpdir = getenv("TMPDIR");
    if (tmpdir) {
        return String(tmpdir);
    }
    return "/tmp";
#elif PLATFORM_WINDOWS
    char buffer[MAX_PATH];
    DWORD length = GetTempPathA(MAX_PATH, buffer);
    if (length > 0 && length < MAX_PATH) {
        return String(buffer);
    }
    return "";
#endif
}

void showMessageBox(const String& title, const String& message, bool isError) {
#if PLATFORM_LINUX
    // For Linux, we'll just print to stderr/stdout for now
    // In a full implementation, we might use GTK or Qt dialogs
    if (isError) {
        std::cerr << "[ERROR] " << title << ": " << message << std::endl;
    } else {
        std::cout << "[INFO] " << title << ": " << message << std::endl;
    }
#elif PLATFORM_WINDOWS
    UINT type = isError ? (MB_OK | MB_ICONERROR) : (MB_OK | MB_ICONINFORMATION);
    MessageBoxA(nullptr, message.c_str(), title.c_str(), type);
#endif
}

bool isDebuggerPresent() {
    return s_platformInfo.isDebuggerPresent;
}

void debugBreak() {
#if PLATFORM_LINUX
    if (isDebuggerPresent()) {
        __builtin_trap();
    }
#elif PLATFORM_WINDOWS
    if (IsDebuggerPresent()) {
        DebugBreak();
    }
#endif
}

uint32 getCurrentThreadId() {
#if PLATFORM_LINUX
    return static_cast<uint32>(pthread_self());
#elif PLATFORM_WINDOWS
    return GetCurrentThreadId();
#endif
}

void setThreadName(const String& name) {
#if PLATFORM_LINUX
    pthread_setname_np(pthread_self(), name.c_str());
#elif PLATFORM_WINDOWS
    // Windows thread naming requires more complex implementation
    // For now, we'll skip this
#endif
}

uint32 getCpuCoreCount() {
    return s_platformInfo.cpuCoreCount;
}

uint32 getCacheLineSize() {
    return CACHE_LINE_SIZE;
}

void sleep(uint32 milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}

void yield() {
    std::this_thread::yield();
}

uint64 getHighResolutionTime() {
    auto now = std::chrono::high_resolution_clock::now();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
}

uint64 getHighResolutionFrequency() {
    return 1000000000ULL; // nanoseconds per second
}

} // namespace Platform
} // namespace PlaytimeEngine
