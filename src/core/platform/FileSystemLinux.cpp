/**
 * @file FileSystemLinux.cpp
 * @brief Linux-specific file system operations
 */

#include "platform/FileSystem.h"
#include "Platform.h"

#if PLATFORM_LINUX

#include <sys/stat.h>
#include <sys/types.h>
#include <dirent.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <fstream>
#include <cstring>

namespace PlaytimeEngine {
namespace FileSystem {

bool exists(const String& path) {
    struct stat st;
    return stat(path.c_str(), &st) == 0;
}

bool isDirectory(const String& path) {
    struct stat st;
    if (stat(path.c_str(), &st) != 0) {
        return false;
    }
    return S_ISDIR(st.st_mode);
}

bool isFile(const String& path) {
    struct stat st;
    if (stat(path.c_str(), &st) != 0) {
        return false;
    }
    return S_ISREG(st.st_mode);
}

uint64 getFileSize(const String& path) {
    struct stat st;
    if (stat(path.c_str(), &st) != 0) {
        return 0;
    }
    return static_cast<uint64>(st.st_size);
}

uint64 getLastModified(const String& path) {
    struct stat st;
    if (stat(path.c_str(), &st) != 0) {
        return 0;
    }
    return static_cast<uint64>(st.st_mtime);
}

bool createDirectory(const String& path) {
    return mkdir(path.c_str(), 0755) == 0 || errno == EEXIST;
}

bool createDirectories(const String& path) {
    if (path.empty()) {
        return false;
    }
    
    if (exists(path)) {
        return isDirectory(path);
    }
    
    String parent = Platform::getParentDirectory(path);
    if (!parent.empty() && parent != path) {
        if (!createDirectories(parent)) {
            return false;
        }
    }
    
    return createDirectory(path);
}

bool deleteFile(const String& path) {
    return unlink(path.c_str()) == 0;
}

bool deleteDirectory(const String& path, bool recursive) {
    if (!recursive) {
        return rmdir(path.c_str()) == 0;
    }
    
    // Recursive deletion
    DIR* dir = opendir(path.c_str());
    if (!dir) {
        return false;
    }
    
    struct dirent* entry;
    bool success = true;
    
    while ((entry = readdir(dir)) != nullptr) {
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }
        
        String fullPath = Platform::combinePaths(path, entry->d_name);
        
        if (isDirectory(fullPath)) {
            if (!deleteDirectory(fullPath, true)) {
                success = false;
            }
        } else {
            if (!deleteFile(fullPath)) {
                success = false;
            }
        }
    }
    
    closedir(dir);
    
    if (success) {
        success = rmdir(path.c_str()) == 0;
    }
    
    return success;
}

bool copyFile(const String& source, const String& destination) {
    std::ifstream src(source, std::ios::binary);
    if (!src.is_open()) {
        return false;
    }
    
    std::ofstream dst(destination, std::ios::binary);
    if (!dst.is_open()) {
        return false;
    }
    
    dst << src.rdbuf();
    
    return src.good() && dst.good();
}

bool moveFile(const String& source, const String& destination) {
    return rename(source.c_str(), destination.c_str()) == 0;
}

std::vector<FileInfo> listDirectory(const String& path) {
    std::vector<FileInfo> result;
    
    DIR* dir = opendir(path.c_str());
    if (!dir) {
        return result;
    }
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }
        
        FileInfo info;
        info.name = entry->d_name;
        info.fullPath = Platform::combinePaths(path, entry->d_name);
        
        struct stat st;
        if (stat(info.fullPath.c_str(), &st) == 0) {
            info.size = static_cast<uint64>(st.st_size);
            info.lastModified = static_cast<uint64>(st.st_mtime);
            info.isDirectory = S_ISDIR(st.st_mode);
            info.isReadOnly = !(st.st_mode & S_IWUSR);
            info.isHidden = info.name[0] == '.';
        } else {
            info.size = 0;
            info.lastModified = 0;
            info.isDirectory = false;
            info.isReadOnly = false;
            info.isHidden = info.name[0] == '.';
        }
        
        result.push_back(info);
    }
    
    closedir(dir);
    return result;
}

String readTextFile(const String& path) {
    std::ifstream file(path);
    if (!file.is_open()) {
        return "";
    }
    
    String content;
    String line;
    while (std::getline(file, line)) {
        content += line + "\n";
    }
    
    return content;
}

std::vector<uint8> readBinaryFile(const String& path) {
    std::ifstream file(path, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
        return {};
    }
    
    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<uint8> buffer(size);
    if (!file.read(reinterpret_cast<char*>(buffer.data()), size)) {
        return {};
    }
    
    return buffer;
}

bool writeTextFile(const String& path, const String& content) {
    std::ofstream file(path);
    if (!file.is_open()) {
        return false;
    }
    
    file << content;
    return file.good();
}

bool writeBinaryFile(const String& path, const std::vector<uint8>& data) {
    std::ofstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    file.write(reinterpret_cast<const char*>(data.data()), data.size());
    return file.good();
}

bool appendTextFile(const String& path, const String& content) {
    std::ofstream file(path, std::ios::app);
    if (!file.is_open()) {
        return false;
    }
    
    file << content;
    return file.good();
}

} // namespace FileSystem
} // namespace PlaytimeEngine

#endif // PLATFORM_LINUX
