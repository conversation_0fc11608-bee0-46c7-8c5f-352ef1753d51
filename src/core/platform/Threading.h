#pragma once

/**
 * @file Threading.h
 * @brief Cross-platform threading primitives
 */

#include "../Types.h"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>

namespace PlaytimeEngine {
namespace Threading {

/**
 * @brief Thread handle type
 */
using ThreadHandle = std::thread::id;

/**
 * @brief Mutex type
 */
using Mutex = std::mutex;

/**
 * @brief Recursive mutex type
 */
using RecursiveMutex = std::recursive_mutex;

/**
 * @brief Condition variable type
 */
using ConditionVariable = std::condition_variable;

/**
 * @brief Lock guard type
 */
template<typename MutexType>
using LockGuard = std::lock_guard<MutexType>;

/**
 * @brief Unique lock type
 */
template<typename MutexType>
using UniqueLock = std::unique_lock<MutexType>;

/**
 * @brief Thread priority levels
 */
enum class ThreadPriority {
    Low,
    Normal,
    High,
    Critical
};

/**
 * @brief Create a new thread
 */
template<typename Func, typename... Args>
std::thread createThread(Func&& func, Args&&... args) {
    return std::thread(std::forward<Func>(func), std::forward<Args>(args)...);
}

/**
 * @brief Get current thread ID
 */
ENGINE_API ThreadHandle getCurrentThreadId();

/**
 * @brief Set thread name
 */
ENGINE_API void setThreadName(const String& name);

/**
 * @brief Set thread priority
 */
ENGINE_API bool setThreadPriority(std::thread& thread, ThreadPriority priority);

/**
 * @brief Set thread affinity
 */
ENGINE_API bool setThreadAffinity(std::thread& thread, uint64 coreMask);

/**
 * @brief Get number of hardware threads
 */
ENGINE_API uint32 getHardwareThreadCount();

/**
 * @brief Sleep current thread
 */
ENGINE_API void sleep(uint32 milliseconds);

/**
 * @brief Yield current thread
 */
ENGINE_API void yield();

/**
 * @brief Spin-wait for a short time
 */
ENGINE_API void spinWait(uint32 iterations);

/**
 * @brief Atomic operations
 */
namespace Atomic {
    /**
     * @brief Atomic compare and swap
     */
    template<typename T>
    bool compareAndSwap(std::atomic<T>& target, T expected, T desired) {
        return target.compare_exchange_strong(expected, desired);
    }
    
    /**
     * @brief Atomic fetch and add
     */
    template<typename T>
    T fetchAndAdd(std::atomic<T>& target, T value) {
        return target.fetch_add(value);
    }
    
    /**
     * @brief Atomic fetch and subtract
     */
    template<typename T>
    T fetchAndSubtract(std::atomic<T>& target, T value) {
        return target.fetch_sub(value);
    }
    
    /**
     * @brief Memory fence
     */
    ENGINE_API void memoryFence();
    
    /**
     * @brief Acquire fence
     */
    ENGINE_API void acquireFence();
    
    /**
     * @brief Release fence
     */
    ENGINE_API void releaseFence();
}

/**
 * @brief Spin lock implementation
 */
class ENGINE_API SpinLock {
public:
    SpinLock() : flag_(false) {}
    
    void lock() {
        while (flag_.exchange(true, std::memory_order_acquire)) {
            while (flag_.load(std::memory_order_relaxed)) {
                yield();
            }
        }
    }
    
    bool tryLock() {
        return !flag_.exchange(true, std::memory_order_acquire);
    }
    
    void unlock() {
        flag_.store(false, std::memory_order_release);
    }

private:
    std::atomic<bool> flag_;
};

/**
 * @brief Read-write lock implementation
 */
class ENGINE_API ReadWriteLock {
public:
    ReadWriteLock() : readers_(0), writer_(false) {}
    
    void lockRead() {
        std::unique_lock<std::mutex> lock(mutex_);
        condition_.wait(lock, [this] { return !writer_; });
        ++readers_;
    }
    
    void unlockRead() {
        std::lock_guard<std::mutex> lock(mutex_);
        --readers_;
        if (readers_ == 0) {
            condition_.notify_all();
        }
    }
    
    void lockWrite() {
        std::unique_lock<std::mutex> lock(mutex_);
        condition_.wait(lock, [this] { return !writer_ && readers_ == 0; });
        writer_ = true;
    }
    
    void unlockWrite() {
        std::lock_guard<std::mutex> lock(mutex_);
        writer_ = false;
        condition_.notify_all();
    }

private:
    std::mutex mutex_;
    std::condition_variable condition_;
    uint32 readers_;
    bool writer_;
};

/**
 * @brief Semaphore implementation
 */
class ENGINE_API Semaphore {
public:
    explicit Semaphore(uint32 count = 0) : count_(count) {}
    
    void wait() {
        std::unique_lock<std::mutex> lock(mutex_);
        condition_.wait(lock, [this] { return count_ > 0; });
        --count_;
    }
    
    bool tryWait() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (count_ > 0) {
            --count_;
            return true;
        }
        return false;
    }
    
    void signal() {
        std::lock_guard<std::mutex> lock(mutex_);
        ++count_;
        condition_.notify_one();
    }

private:
    std::mutex mutex_;
    std::condition_variable condition_;
    uint32 count_;
};

} // namespace Threading
} // namespace PlaytimeEngine
