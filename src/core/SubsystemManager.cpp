/**
 * @file SubsystemManager.cpp
 * @brief Subsystem manager implementation
 */

#include "SubsystemManager.h"
#include "Logger.h"
#include <algorithm>

namespace PlaytimeEngine {

SubsystemManager::SubsystemManager() : initialized_(false) {
}

SubsystemManager::~SubsystemManager() {
    if (initialized_) {
        shutdownAll();
    }
}

bool SubsystemManager::initializeAll() {
    if (initialized_) {
        LOG_WARNING("SubsystemManager already initialized");
        return true;
    }
    
    LOG_INFO("Initializing %zu subsystems", subsystems_.size());
    
    // Resolve dependencies and get initialization order
    if (!resolveDependencies()) {
        LOG_ERROR("Failed to resolve subsystem dependencies");
        return false;
    }
    
    // Initialize subsystems in dependency order
    for (const auto& typeIndex : initializationOrder_) {
        auto it = subsystems_.find(typeIndex);
        if (it != subsystems_.end()) {
            LOG_INFO("Initializing subsystem: %s", it->second->getName());
            
            if (!it->second->initialize()) {
                LOG_ERROR("Failed to initialize subsystem: %s", it->second->getName());
                return false;
            }
        }
    }
    
    initialized_ = true;
    LOG_INFO("All subsystems initialized successfully");
    return true;
}

void SubsystemManager::updateAll(float deltaTime) {
    if (!initialized_) {
        return;
    }
    
    // Update subsystems in initialization order
    for (const auto& typeIndex : initializationOrder_) {
        auto it = subsystems_.find(typeIndex);
        if (it != subsystems_.end()) {
            it->second->update(deltaTime);
        }
    }
}

void SubsystemManager::shutdownAll() {
    if (!initialized_) {
        return;
    }
    
    LOG_INFO("Shutting down subsystems");
    
    // Shutdown subsystems in reverse order
    for (auto it = initializationOrder_.rbegin(); it != initializationOrder_.rend(); ++it) {
        auto subsystemIt = subsystems_.find(*it);
        if (subsystemIt != subsystems_.end()) {
            LOG_INFO("Shutting down subsystem: %s", subsystemIt->second->getName());
            subsystemIt->second->shutdown();
        }
    }
    
    initialized_ = false;
    LOG_INFO("All subsystems shut down");
}

bool SubsystemManager::resolveDependencies() {
    // For now, use the registration order as initialization order
    // In the future, implement proper topological sorting based on dependencies
    return true;
}

std::vector<std::type_index> SubsystemManager::topologicalSort() {
    // Placeholder for topological sort implementation
    // For now, return the registration order
    return initializationOrder_;
}

} // namespace PlaytimeEngine
