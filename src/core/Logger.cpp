/**
 * @file Logger.cpp
 * @brief Logger implementation for thread-safe logging
 */

#include "Logger.h"
#include "String.h"
#include <iostream>
#include <cstdarg>
#include <ctime>
#include <chrono>
#include <iomanip>
#include <sstream>

namespace PlaytimeEngine {

Logger& Logger::getInstance() {
    static Logger instance;
    return instance;
}

bool Logger::initialize(const String& filename, LogLevel level) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return true;
    }
    
    level_ = level;
    
    // Open log file if filename provided
    if (!filename.empty()) {
        fileStream_.open(filename.c_str(), std::ios::out | std::ios::app);
        if (!fileStream_.is_open()) {
            std::cerr << "Failed to open log file: " << filename.c_str() << std::endl;
            return false;
        }
        
        // Write initialization message
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        fileStream_ << "\n=== Logger initialized at " 
                   << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") 
                   << " ===" << std::endl;
        fileStream_.flush();
    }
    
    initialized_ = true;
    
    // Log initialization success
    log(LogLevel::Info, "Logger initialized successfully");
    
    return true;
}

void Logger::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    log(LogLevel::Info, "Logger shutting down");
    
    if (fileStream_.is_open()) {
        fileStream_ << "=== Logger shutdown ===" << std::endl;
        fileStream_.close();
    }
    
    initialized_ = false;
}

void Logger::log(LogLevel level, const char* format, ...) {
    if (level < level_) {
        return;
    }
    
    // Format the message
    va_list args;
    va_start(args, format);
    
    // Get required buffer size
    va_list args_copy;
    va_copy(args_copy, args);
    int size = vsnprintf(nullptr, 0, format, args_copy);
    va_end(args_copy);
    
    if (size <= 0) {
        va_end(args);
        return;
    }
    
    // Format the message
    std::vector<char> buffer(size + 1);
    vsnprintf(buffer.data(), buffer.size(), format, args);
    va_end(args);
    
    String message(buffer.data());
    writeLog(level, message);
}

void Logger::logWithLocation(LogLevel level, const char* file, int line, const char* format, ...) {
    if (level < level_) {
        return;
    }
    
    // Format the message
    va_list args;
    va_start(args, format);
    
    // Get required buffer size
    va_list args_copy;
    va_copy(args_copy, args);
    int size = vsnprintf(nullptr, 0, format, args_copy);
    va_end(args_copy);
    
    if (size <= 0) {
        va_end(args);
        return;
    }
    
    // Format the message
    std::vector<char> buffer(size + 1);
    vsnprintf(buffer.data(), buffer.size(), format, args);
    va_end(args);
    
    // Extract filename from full path
    const char* filename = file;
    const char* lastSlash = strrchr(file, '/');
    if (lastSlash) {
        filename = lastSlash + 1;
    }
    const char* lastBackslash = strrchr(filename, '\\');
    if (lastBackslash) {
        filename = lastBackslash + 1;
    }
    
    // Create message with location info
    std::ostringstream oss;
    oss << buffer.data() << " [" << filename << ":" << line << "]";
    
    String message(oss.str().c_str());
    writeLog(level, message);
}

void Logger::flush() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::cout.flush();
    if (fileStream_.is_open()) {
        fileStream_.flush();
    }
}

void Logger::writeLog(LogLevel level, const String& message) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    // Get current time
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    // Get milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    // Format timestamp
    std::ostringstream timestamp;
    timestamp << std::put_time(&tm, "%H:%M:%S") << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    // Format log entry
    std::ostringstream logEntry;
    logEntry << "[" << timestamp.str() << "] [" << getLevelString(level) << "] " << message.c_str();
    
    // Output to console
    if (level >= LogLevel::Error) {
        std::cerr << logEntry.str() << std::endl;
    } else {
        std::cout << logEntry.str() << std::endl;
    }
    
    // Output to file
    if (fileStream_.is_open()) {
        fileStream_ << logEntry.str() << std::endl;
    }
}

const char* Logger::getLevelString(LogLevel level) {
    switch (level) {
        case LogLevel::Trace:    return "TRACE";
        case LogLevel::Debug:    return "DEBUG";
        case LogLevel::Info:     return "INFO ";
        case LogLevel::Warning:  return "WARN ";
        case LogLevel::Error:    return "ERROR";
        case LogLevel::Critical: return "CRIT ";
        default:                 return "UNKN ";
    }
}

} // namespace PlaytimeEngine
