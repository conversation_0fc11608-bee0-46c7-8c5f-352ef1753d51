#pragma once

/**
 * @file JobSystem.h
 * @brief High-performance job system with work-stealing
 */

#include "Types.h"
#include "jobs/Job.h"
#include "jobs/JobHandle.h"
#include "jobs/WorkStealingDeque.h"
#include "jobs/ParallelFor.h"
#include <vector>
#include <thread>
#include <atomic>

namespace PlaytimeEngine {

/**
 * @brief High-performance job system for multi-threading
 */
class ENGINE_API JobSystem : public NonCopyable {
public:
    /**
     * @brief Job system statistics
     */
    struct Stats {
        uint32 workerCount = 0;
        uint64 jobsExecuted = 0;
        uint64 jobsStolen = 0;
        uint64 totalExecutionTime = 0;
        float averageJobTime = 0.0f;
        uint32 activeJobs = 0;
        uint32 queuedJobs = 0;
    };

    JobSystem();
    ~JobSystem();
    
    /**
     * @brief Initialize the job system
     * @param workerCount Number of worker threads (0 = auto-detect)
     */
    bool initialize(uint32 workerCount = 0);
    
    /**
     * @brief Shutdown the job system
     */
    void shutdown();
    
    /**
     * @brief Submit a job for execution
     * @param job Job to execute
     * @return Job handle for tracking completion
     */
    JobHandle submit(UniquePtr<IJob> job);
    
    /**
     * @brief Submit a job with dependencies
     * @param job Job to execute
     * @param dependencies Jobs that must complete first
     * @return Job handle for tracking completion
     */
    JobHandle submit(UniquePtr<IJob> job, const std::vector<JobHandle>& dependencies);
    
    /**
     * @brief Submit a lambda as a job
     * @param func Lambda function to execute
     * @return Job handle for tracking completion
     */
    template<typename Func>
    JobHandle submitLambda(Func&& func) {
        auto job = std::make_unique<LambdaJob<Func>>(std::forward<Func>(func));
        return submit(std::move(job));
    }
    
    /**
     * @brief Submit a parallel-for job
     * @param start Start index
     * @param end End index
     * @param func Function to execute for each index
     * @param batchSize Number of items per batch
     * @return Job handle for tracking completion
     */
    template<typename Func>
    JobHandle submitParallelFor(uint32 start, uint32 end, Func&& func, uint32 batchSize = 1) {
        auto job = std::make_unique<ParallelForJob<Func>>(start, end, std::forward<Func>(func), batchSize);
        return submit(std::move(job));
    }
    
    /**
     * @brief Wait for a job to complete
     * @param handle Job handle to wait for
     */
    void wait(const JobHandle& handle);
    
    /**
     * @brief Wait for multiple jobs to complete
     * @param handles Job handles to wait for
     */
    void waitAll(const std::vector<JobHandle>& handles);
    
    /**
     * @brief Check if a job is complete
     * @param handle Job handle to check
     * @return True if the job is complete
     */
    bool isComplete(const JobHandle& handle) const;
    
    /**
     * @brief Execute jobs on the current thread until condition is met
     * @param condition Function that returns true when done
     */
    void executeUntil(std::function<bool()> condition);
    
    /**
     * @brief Execute a single job on the current thread
     * @return True if a job was executed
     */
    bool executeOne();
    
    /**
     * @brief Get the number of worker threads
     */
    uint32 getWorkerCount() const { return workerCount_; }
    
    /**
     * @brief Get the current thread's worker index
     * @return Worker index, or UINT32_MAX if not a worker thread
     */
    uint32 getCurrentWorkerIndex() const;
    
    /**
     * @brief Check if the current thread is a worker thread
     */
    bool isWorkerThread() const;
    
    /**
     * @brief Get job system statistics
     */
    Stats getStats() const;
    
    /**
     * @brief Reset statistics
     */
    void resetStats();
    
    /**
     * @brief Set job system priority
     * @param priority Thread priority
     */
    void setPriority(int priority);

private:
    /**
     * @brief Worker thread data
     */
    struct WorkerThread {
        std::thread thread;
        WorkStealingDeque<IJob*> jobQueue;
        uint32 index;
        std::atomic<bool> active{true};
        
        // Statistics
        std::atomic<uint64> jobsExecuted{0};
        std::atomic<uint64> jobsStolen{0};
    };
    
    /**
     * @brief Worker thread main loop
     */
    void workerMain(uint32 workerIndex);
    
    /**
     * @brief Try to get a job from any queue
     */
    IJob* getJob(uint32 workerIndex);
    
    /**
     * @brief Try to steal a job from another worker
     */
    IJob* stealJob(uint32 excludeWorker);
    
    /**
     * @brief Execute a job
     */
    void executeJob(IJob* job);
    
    /**
     * @brief Get optimal worker count
     */
    uint32 getOptimalWorkerCount() const;

private:
    std::vector<UniquePtr<WorkerThread>> workers_;
    uint32 workerCount_;
    std::atomic<bool> shutdown_;
    std::atomic<uint32> nextJobId_;
    
    // Thread-local storage for worker index
    static thread_local uint32 currentWorkerIndex_;
    
    // Statistics
    mutable std::atomic<uint64> totalJobsExecuted_;
    mutable std::atomic<uint64> totalJobsStolen_;
    mutable std::atomic<uint64> totalExecutionTime_;
};

/**
 * @brief Global job system functions
 */
namespace Jobs {
    /**
     * @brief Initialize global job system
     */
    ENGINE_API bool initialize(uint32 workerCount = 0);
    
    /**
     * @brief Shutdown global job system
     */
    ENGINE_API void shutdown();
    
    /**
     * @brief Get the global job system
     */
    ENGINE_API JobSystem* getSystem();
    
    /**
     * @brief Submit a job to the global system
     */
    ENGINE_API JobHandle submit(UniquePtr<IJob> job);
    
    /**
     * @brief Submit a lambda to the global system
     */
    template<typename Func>
    JobHandle submitLambda(Func&& func) {
        return getSystem()->submitLambda(std::forward<Func>(func));
    }
    
    /**
     * @brief Submit a parallel-for to the global system
     */
    template<typename Func>
    JobHandle submitParallelFor(uint32 start, uint32 end, Func&& func, uint32 batchSize = 1) {
        return getSystem()->submitParallelFor(start, end, std::forward<Func>(func), batchSize);
    }
    
    /**
     * @brief Wait for a job to complete
     */
    ENGINE_API void wait(const JobHandle& handle);
    
    /**
     * @brief Wait for multiple jobs to complete
     */
    ENGINE_API void waitAll(const std::vector<JobHandle>& handles);
    
    /**
     * @brief Check if a job is complete
     */
    ENGINE_API bool isComplete(const JobHandle& handle);
}

} // namespace PlaytimeEngine
