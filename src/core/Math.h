#pragma once

/**
 * @file Math.h
 * @brief Core mathematics library for Project Playtime Engine
 * 
 * This header provides the main interface to the engine's math library,
 * including vectors, matrices, quaternions, and common mathematical functions.
 * All math operations are optimized for game engine performance with SIMD support.
 */

#include "math/Vector.h"
#include "math/Matrix.h"
#include "math/Quaternion.h"
#include "math/MathUtils.h"

namespace PlaytimeEngine {
namespace Math {

// Common mathematical constants
constexpr float PI = 3.14159265358979323846f;
constexpr float TWO_PI = 2.0f * PI;
constexpr float HALF_PI = PI * 0.5f;
constexpr float QUARTER_PI = PI * 0.25f;
constexpr float INV_PI = 1.0f / PI;
constexpr float INV_TWO_PI = 1.0f / TWO_PI;

constexpr float E = 2.71828182845904523536f;
constexpr float LOG2E = 1.44269504088896340736f;
constexpr float LOG10E = 0.434294481903251827651f;
constexpr float LN2 = 0.693147180559945309417f;
constexpr float LN10 = 2.30258509299404568402f;

constexpr float SQRT2 = 1.41421356237309504880f;
constexpr float SQRT3 = 1.73205080756887729353f;
constexpr float INV_SQRT2 = 0.707106781186547524401f;
constexpr float INV_SQRT3 = 0.577350269189625764509f;

constexpr float DEG_TO_RAD = PI / 180.0f;
constexpr float RAD_TO_DEG = 180.0f / PI;

constexpr float EPSILON = 1e-6f;
constexpr float FLOAT_MIN = 1.175494351e-38f;
constexpr float FLOAT_MAX = 3.402823466e+38f;

// Type aliases for convenience
using Vec2 = Vector2;
using Vec3 = Vector3;
using Vec4 = Vector4;
using Mat3 = Matrix3x3;
using Mat4 = Matrix4x4;
using Quat = Quaternion;

// Common vector constants
const Vec2 VEC2_ZERO(0.0f, 0.0f);
const Vec2 VEC2_ONE(1.0f, 1.0f);
const Vec2 VEC2_UNIT_X(1.0f, 0.0f);
const Vec2 VEC2_UNIT_Y(0.0f, 1.0f);

const Vec3 VEC3_ZERO(0.0f, 0.0f, 0.0f);
const Vec3 VEC3_ONE(1.0f, 1.0f, 1.0f);
const Vec3 VEC3_UNIT_X(1.0f, 0.0f, 0.0f);
const Vec3 VEC3_UNIT_Y(0.0f, 1.0f, 0.0f);
const Vec3 VEC3_UNIT_Z(0.0f, 0.0f, 1.0f);
const Vec3 VEC3_UP(0.0f, 1.0f, 0.0f);
const Vec3 VEC3_DOWN(0.0f, -1.0f, 0.0f);
const Vec3 VEC3_LEFT(-1.0f, 0.0f, 0.0f);
const Vec3 VEC3_RIGHT(1.0f, 0.0f, 0.0f);
const Vec3 VEC3_FORWARD(0.0f, 0.0f, 1.0f);
const Vec3 VEC3_BACKWARD(0.0f, 0.0f, -1.0f);

const Vec4 VEC4_ZERO(0.0f, 0.0f, 0.0f, 0.0f);
const Vec4 VEC4_ONE(1.0f, 1.0f, 1.0f, 1.0f);
const Vec4 VEC4_UNIT_X(1.0f, 0.0f, 0.0f, 0.0f);
const Vec4 VEC4_UNIT_Y(0.0f, 1.0f, 0.0f, 0.0f);
const Vec4 VEC4_UNIT_Z(0.0f, 0.0f, 1.0f, 0.0f);
const Vec4 VEC4_UNIT_W(0.0f, 0.0f, 0.0f, 1.0f);

// Common matrix constants
const Mat3 MAT3_IDENTITY = Mat3::identity();
const Mat3 MAT3_ZERO = Mat3::zero();

const Mat4 MAT4_IDENTITY = Mat4::identity();
const Mat4 MAT4_ZERO = Mat4::zero();

// Common quaternion constants
const Quat QUAT_IDENTITY = Quat::identity();

/**
 * @brief Initialize the math library
 * 
 * This function should be called once during engine initialization
 * to set up any platform-specific optimizations or SIMD support.
 */
void initialize();

/**
 * @brief Shutdown the math library
 * 
 * Clean up any resources allocated by the math library.
 */
void shutdown();

/**
 * @brief Check if the math library supports SIMD operations
 * @return True if SIMD is supported and enabled
 */
bool isSimdSupported();

/**
 * @brief Get the SIMD instruction set being used
 * @return String describing the SIMD instruction set (e.g., "SSE4.1", "AVX2", "NEON")
 */
const char* getSimdInstructionSet();

} // namespace Math
} // namespace PlaytimeEngine

// Global math function aliases for convenience
using namespace PlaytimeEngine::Math;
