# Core Engine Subsystem

# Create subdirectories for organization
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/math)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/containers)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/platform)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/jobs)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/memory)

# Core library source files
set(CORE_SOURCES
    # Main core files
    Engine.cpp
    Application.cpp
    SubsystemManager.cpp
    GameLoop.cpp
    EngineConfig.cpp

    # Math library
    math/Vector.cpp
    math/Matrix.cpp
    math/Quaternion.cpp
    math/MathUtils.cpp

    # Containers
    containers/String.cpp

    # Platform abstraction
    platform/Platform.cpp
    platform/FileSystem.cpp
    platform/Threading.cpp
    platform/DynamicLibrary.cpp
    platform/Timer.cpp

    # Job system
    jobs/JobSystem.cpp
    jobs/Job.cpp
    jobs/WorkStealingDeque.cpp
    jobs/JobHandle.cpp
    jobs/ParallelFor.cpp

    # Memory management
    memory/MemoryManager.cpp
    memory/PoolAllocator.cpp
    memory/StackAllocator.cpp
    memory/LinearAllocator.cpp
    memory/MemoryTracker.cpp
)

# Platform-specific sources
if(PLATFORM_WINDOWS)
    list(APPEND CORE_SOURCES
        platform/PlatformWin.cpp
        platform/FileSystemWin.cpp
        platform/ThreadingWin.cpp
        platform/DynamicLibraryWin.cpp
        platform/TimerWin.cpp
    )
elseif(PLATFORM_LINUX)
    list(APPEND CORE_SOURCES
        platform/PlatformLinux.cpp
        platform/FileSystemLinux.cpp
        platform/ThreadingLinux.cpp
        platform/DynamicLibraryLinux.cpp
        platform/TimerLinux.cpp
    )
endif()

# Core library headers
set(CORE_HEADERS
    # Main headers
    Engine.h
    Application.h
    SubsystemManager.h
    GameLoop.h
    EngineConfig.h
    Types.h

    # Math library
    Math.h
    math/Vector.h
    math/Matrix.h
    math/Quaternion.h
    math/MathUtils.h

    # Containers
    containers/Array.h
    containers/HashMap.h
    String.h

    # Platform abstraction
    Platform.h
    platform/FileSystem.h
    platform/Threading.h
    platform/DynamicLibrary.h
    platform/Timer.h

    # Job system
    JobSystem.h
    jobs/Job.h
    jobs/WorkStealingDeque.h
    jobs/JobHandle.h
    jobs/ParallelFor.h

    # Memory management
    Memory.h
    memory/IAllocator.h
    memory/PoolAllocator.h
    memory/StackAllocator.h
    memory/LinearAllocator.h
    memory/MemoryTracker.h

    # Utilities
    Hash.h
    Logger.h
    Assert.h
)

# Create the core library
add_library(EngineCore STATIC ${CORE_SOURCES} ${CORE_HEADERS})

# Include directories
target_include_directories(EngineCore
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Platform-specific linking
if(PLATFORM_WINDOWS)
    target_link_libraries(EngineCore
        PRIVATE
            kernel32
            user32
            winmm
    )
elseif(PLATFORM_LINUX)
    target_link_libraries(EngineCore
        PRIVATE
            pthread
            dl
            rt
    )
endif()

# External dependencies (conditional linking)
if(SDL2_FOUND)
    target_link_libraries(EngineCore
        PUBLIC
            ${SDL2_LIBRARIES}
    )
    target_include_directories(EngineCore
        PUBLIC
            ${SDL2_INCLUDE_DIRS}
    )
endif()

# Compiler definitions
target_compile_definitions(EngineCore
    PUBLIC
        ENGINE_CORE_EXPORTS
    PRIVATE
        $<$<CONFIG:Debug>:ENGINE_DEBUG>
        $<$<CONFIG:Release>:ENGINE_RELEASE>
)

# Set target properties
set_target_properties(EngineCore PROPERTIES
    OUTPUT_NAME "EngineCore"
    FOLDER "Engine/Core"
)

# Copy headers to include directory
file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/include/core)
foreach(HEADER ${CORE_HEADERS})
    get_filename_component(HEADER_DIR ${HEADER} DIRECTORY)
    if(HEADER_DIR)
        file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/include/core/${HEADER_DIR})
    endif()
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/${HEADER}
        ${CMAKE_SOURCE_DIR}/include/core/${HEADER}
        COPYONLY
    )
endforeach()
