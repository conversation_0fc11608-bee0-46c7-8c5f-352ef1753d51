/**
 * @file Engine.cpp
 * @brief Main engine implementation
 */

#include "Engine.h"
#include "Platform.h"
#include "Logger.h"
#include "JobSystem.h"
#include "Memory.h"

namespace PlaytimeEngine {

// Static instance
Engine* Engine::instance_ = nullptr;

Engine::Engine() 
    : state_(State::Uninitialized)
    , shutdownRequested_(false)
    , renderer_(nullptr)
    , inputManager_(nullptr)
    , audioEngine_(nullptr)
    , physicsWorld_(nullptr)
    , sceneManager_(nullptr) {
    
    // Set singleton instance
    instance_ = this;
}

Engine::~Engine() {
    if (state_ != State::Shutdown) {
        shutdown();
    }
    
    // Clear singleton instance
    if (instance_ == this) {
        instance_ = nullptr;
    }
}

Engine::InitResult Engine::initialize(const EngineConfig& config) {
    if (state_ != State::Uninitialized) {
        LOG_ERROR("Engine is already initialized");
        return InitResult::ConfigurationError;
    }
    
    state_ = State::Initializing;
    config_ = config;
    
    LOG_INFO("Initializing Project Playtime Engine v%d.%d.%d", 
             ENGINE_VERSION_MAJOR, ENGINE_VERSION_MINOR, ENGINE_VERSION_PATCH);
    
    // Initialize core systems
    if (!initializeCore()) {
        LOG_ERROR("Failed to initialize core systems");
        state_ = State::Uninitialized;
        return InitResult::SubsystemError;
    }
    
    // Initialize platform layer
    if (!initializePlatform()) {
        LOG_ERROR("Failed to initialize platform layer");
        state_ = State::Uninitialized;
        return InitResult::PlatformError;
    }
    
    // Initialize renderer
    if (!initializeRenderer()) {
        LOG_ERROR("Failed to initialize renderer");
        state_ = State::Uninitialized;
        return InitResult::RendererError;
    }
    
    // Initialize other subsystems
    if (!initializeSubsystems()) {
        LOG_ERROR("Failed to initialize subsystems");
        state_ = State::Uninitialized;
        return InitResult::SubsystemError;
    }
    
    // Initialize plugins
    if (!initializePlugins()) {
        LOG_ERROR("Failed to initialize plugins");
        state_ = State::Uninitialized;
        return InitResult::SubsystemError;
    }
    
    state_ = State::Running;
    LOG_INFO("Engine initialization complete");
    
    return InitResult::Success;
}

int Engine::run() {
    if (state_ != State::Running) {
        LOG_ERROR("Engine is not in running state");
        return -1;
    }
    
    LOG_INFO("Starting main engine loop");
    
    // Initialize game loop
    if (!gameLoop_->initialize()) {
        LOG_ERROR("Failed to initialize game loop");
        return -1;
    }
    
    // Set up game loop callbacks
    gameLoop_->setUpdateCallback([this](float deltaTime) {
        updateFrame(deltaTime);
    });
    
    gameLoop_->setRenderCallback([this]() {
        renderFrame();
    });
    
    // Main loop
    while (!shutdownRequested_ && gameLoop_->tick()) {
        handleEvents();
    }
    
    LOG_INFO("Main engine loop ended");
    return 0;
}

void Engine::shutdown() {
    if (state_ == State::Shutdown || state_ == State::Shutting_Down) {
        return;
    }
    
    LOG_INFO("Shutting down engine");
    state_ = State::Shutting_Down;
    
    // Shutdown game loop
    if (gameLoop_) {
        gameLoop_->shutdown();
    }
    
    // Shutdown all subsystems
    shutdownSubsystems();
    
    state_ = State::Shutdown;
    LOG_INFO("Engine shutdown complete");
}

void Engine::requestShutdown() {
    shutdownRequested_ = true;
    if (gameLoop_) {
        gameLoop_->requestExit();
    }
}

void Engine::pause() {
    if (state_ == State::Running) {
        state_ = State::Paused;
        if (gameLoop_) {
            gameLoop_->pause();
        }
        LOG_INFO("Engine paused");
    }
}

void Engine::resume() {
    if (state_ == State::Paused) {
        state_ = State::Running;
        if (gameLoop_) {
            gameLoop_->resume();
        }
        LOG_INFO("Engine resumed");
    }
}

bool Engine::initializeCore() {
    // Initialize logger
    if (!Logger::getInstance().initialize(config_.logFile, static_cast<LogLevel>(config_.logLevel))) {
        return false;
    }
    
    // Initialize memory manager
    memoryManager_ = std::make_unique<MemoryManager>();
    if (!memoryManager_->initialize(config_.heapSize)) {
        LOG_ERROR("Failed to initialize memory manager");
        return false;
    }
    
    // Initialize job system
    jobSystem_ = std::make_unique<JobSystem>();
    if (!jobSystem_->initialize(config_.workerThreadCount)) {
        LOG_ERROR("Failed to initialize job system");
        return false;
    }
    
    // Initialize subsystem manager
    subsystemManager_ = std::make_unique<SubsystemManager>();
    
    // Initialize game loop
    gameLoop_ = std::make_unique<GameLoop>();
    
    return true;
}

bool Engine::initializePlatform() {
    auto result = Platform::initialize();
    return result == Platform::InitResult::Success;
}

bool Engine::initializeRenderer() {
    // Renderer initialization will be implemented in future phases
    LOG_INFO("Renderer initialization placeholder");
    return true;
}

bool Engine::initializeSubsystems() {
    // Initialize all registered subsystems
    return subsystemManager_->initializeAll();
}

bool Engine::initializePlugins() {
    // Plugin system initialization will be implemented in future phases
    LOG_INFO("Plugin system initialization placeholder");
    return true;
}

void Engine::shutdownSubsystems() {
    if (subsystemManager_) {
        subsystemManager_->shutdownAll();
    }
    
    if (jobSystem_) {
        jobSystem_->shutdown();
    }
    
    if (memoryManager_) {
        memoryManager_->shutdown();
    }
    
    Logger::getInstance().shutdown();
    Platform::shutdown();
}

void Engine::updateFrame(float deltaTime) {
    if (subsystemManager_) {
        subsystemManager_->updateAll(deltaTime);
    }
}

void Engine::renderFrame() {
    // Rendering will be implemented in future phases
}

void Engine::handleEvents() {
    // Event handling will be implemented in future phases
}

uint64 Engine::getFrameNumber() const {
    return gameLoop_ ? gameLoop_->getStats().frameNumber : 0;
}

float Engine::getDeltaTime() const {
    return gameLoop_ ? gameLoop_->getStats().deltaTime : 0.0f;
}

double Engine::getElapsedTime() const {
    return gameLoop_ ? gameLoop_->getStats().totalTime : 0.0;
}

float Engine::getFPS() const {
    return gameLoop_ ? gameLoop_->getStats().fps : 0.0f;
}

} // namespace PlaytimeEngine
