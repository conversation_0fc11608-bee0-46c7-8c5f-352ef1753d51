/**
 * @file GameLoop.cpp
 * @brief Game loop implementation
 */

#include "GameLoop.h"
#include "Platform.h"
#include "Logger.h"
#include <chrono>
#include <thread>

namespace PlaytimeEngine {

GameLoop::GameLoop()
    : mode_(Mode::VariableTimestep)
    , targetFPS_(60)
    , fixedTimestep_(1.0f / 60.0f)
    , maxDeltaTime_(1.0f / 30.0f)
    , lastFrameTime_(0.0)
    , accumulator_(0.0)
    , initialized_(false)
    , exitRequested_(false)
    , paused_(false)
    , frameHistoryIndex_(0) {
    
    // Initialize frame history
    for (uint32 i = 0; i < FRAME_HISTORY_SIZE; ++i) {
        frameHistory_[i] = 0.0f;
    }
    
    resetStats();
}

GameLoop::~GameLoop() {
    if (initialized_) {
        shutdown();
    }
}

bool GameLoop::initialize() {
    if (initialized_) {
        LOG_WARNING("GameLoop already initialized");
        return true;
    }
    
    LOG_INFO("Initializing game loop");
    
    lastFrameTime_ = getCurrentTime();
    accumulator_ = 0.0;
    exitRequested_ = false;
    paused_ = false;
    
    resetStats();
    
    initialized_ = true;
    LOG_INFO("Game loop initialized");
    return true;
}

bool GameLoop::tick() {
    if (!initialized_ || exitRequested_) {
        return false;
    }
    
    double currentTime = getCurrentTime();
    double frameTime = currentTime - lastFrameTime_;
    lastFrameTime_ = currentTime;
    
    // Update statistics
    updateStats(static_cast<float>(frameTime));
    
    if (paused_) {
        // When paused, just sleep and return
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        return true;
    }
    
    switch (mode_) {
        case Mode::VariableTimestep:
            if (updateCallback_) {
                updateCallback_(static_cast<float>(frameTime));
            }
            break;
            
        case Mode::FixedTimestep:
            accumulator_ += frameTime;
            while (accumulator_ >= fixedTimestep_) {
                if (updateCallback_) {
                    updateCallback_(fixedTimestep_);
                }
                accumulator_ -= fixedTimestep_;
            }
            break;
            
        case Mode::SemiFixed:
            {
                float deltaTime = static_cast<float>(frameTime);
                if (deltaTime > maxDeltaTime_) {
                    deltaTime = maxDeltaTime_;
                }
                if (updateCallback_) {
                    updateCallback_(deltaTime);
                }
            }
            break;
    }
    
    // Render
    if (renderCallback_) {
        renderCallback_();
    }
    
    // Maintain target frame rate
    maintainFrameRate();
    
    return true;
}

void GameLoop::shutdown() {
    if (!initialized_) {
        return;
    }
    
    LOG_INFO("Shutting down game loop");
    
    updateCallback_ = nullptr;
    renderCallback_ = nullptr;
    
    initialized_ = false;
    LOG_INFO("Game loop shut down");
}

void GameLoop::setTargetFrameRate(uint32 fps) {
    targetFPS_ = fps;
    LOG_INFO("Target frame rate set to %u FPS", fps);
}

void GameLoop::resetStats() {
    stats_.deltaTime = 0.0f;
    stats_.frameTime = 0.0f;
    stats_.fps = 0.0f;
    stats_.frameNumber = 0;
    stats_.totalTime = 0.0;
    stats_.minFrameTime = 1000.0f;
    stats_.maxFrameTime = 0.0f;
    stats_.avgFrameTime = 0.0f;
    stats_.frameCount = 0;
    
    frameHistoryIndex_ = 0;
    for (uint32 i = 0; i < FRAME_HISTORY_SIZE; ++i) {
        frameHistory_[i] = 0.0f;
    }
}

void GameLoop::updateStats(float frameTime) {
    stats_.frameTime = frameTime;
    stats_.deltaTime = frameTime;
    stats_.frameNumber++;
    stats_.totalTime += frameTime;
    stats_.frameCount++;
    
    // Update frame time statistics
    if (frameTime < stats_.minFrameTime) {
        stats_.minFrameTime = frameTime;
    }
    if (frameTime > stats_.maxFrameTime) {
        stats_.maxFrameTime = frameTime;
    }
    
    // Update frame history for FPS calculation
    frameHistory_[frameHistoryIndex_] = frameTime;
    frameHistoryIndex_ = (frameHistoryIndex_ + 1) % FRAME_HISTORY_SIZE;
    
    // Calculate average frame time and FPS
    float totalFrameTime = 0.0f;
    uint32 validFrames = std::min(stats_.frameCount, FRAME_HISTORY_SIZE);
    
    for (uint32 i = 0; i < validFrames; ++i) {
        totalFrameTime += frameHistory_[i];
    }
    
    if (validFrames > 0) {
        stats_.avgFrameTime = totalFrameTime / validFrames;
        stats_.fps = stats_.avgFrameTime > 0.0f ? 1.0f / stats_.avgFrameTime : 0.0f;
    }
}

void GameLoop::maintainFrameRate() {
    if (targetFPS_ == 0) {
        return; // No frame rate limiting
    }
    
    double targetFrameTime = 1.0 / targetFPS_;
    double currentTime = getCurrentTime();
    double elapsedTime = currentTime - lastFrameTime_;
    
    if (elapsedTime < targetFrameTime) {
        double sleepTime = targetFrameTime - elapsedTime;
        auto sleepDuration = std::chrono::duration<double>(sleepTime);
        std::this_thread::sleep_for(sleepDuration);
    }
}

double GameLoop::getCurrentTime() const {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

} // namespace PlaytimeEngine
