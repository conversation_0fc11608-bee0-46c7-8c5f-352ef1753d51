# Animation Subsystem - Placeholder

add_library(EngineAnimation STATIC
    placeholder.cpp
)

target_include_directories(EngineAnimation
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EngineAnimation
    PUBLIC
        EngineCore
)

file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for animation subsystem\n"
    "namespace PlaytimeEngine { namespace Animation { void placeholder() {} } }\n"
)
