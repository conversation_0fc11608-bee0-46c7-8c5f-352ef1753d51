# Audio Subsystem - Placeholder

add_library(EngineAudio STATIC
    placeholder.cpp
)

target_include_directories(EngineAudio
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EngineAudio
    PUBLIC
        EngineCore
)

file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for audio subsystem\n"
    "namespace PlaytimeEngine { namespace Audio { void placeholder() {} } }\n"
)
