# Editor Subsystem - Placeholder

add_library(EngineEditor STATIC
    placeholder.cpp
)

target_include_directories(EngineEditor
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EngineEditor
    PUBLIC
        EngineCore
        EngineUI
)

file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for editor subsystem\n"
    "namespace PlaytimeEngine { namespace Editor { void placeholder() {} } }\n"
)
