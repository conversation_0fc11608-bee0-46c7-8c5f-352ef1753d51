/**
 * @file main.cpp
 * @brief Editor application entry point
 */

#include "core/Application.h"
#include "core/Logger.h"

using namespace PlaytimeEngine;

/**
 * @brief Editor application class
 */
class EditorApplication : public Application {
public:
    bool initialize() override {
        LOG_INFO("Initializing Project Playtime Editor");
        return true;
    }
    
    void update(float deltaTime) override {
        // Editor update logic will be implemented in future phases
        (void)deltaTime;
    }
    
    void render() override {
        // Editor rendering will be implemented in future phases
    }
    
    void shutdown() override {
        LOG_INFO("Shutting down Project Playtime Editor");
    }
    
    const char* getName() const override {
        return "Project Playtime Editor";
    }
    
    EngineConfig getEngineConfig() const override {
        EngineConfig config = EngineConfig::getDefault();
        config.renderConfig.windowTitle = "Project Playtime Editor";
        config.debugMode = true;
        return config;
    }
};

IMPLEMENT_APPLICATION(EditorApplication)
