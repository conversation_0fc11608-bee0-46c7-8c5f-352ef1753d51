/**
 * @file main.cpp
 * @brief Sample application entry point
 */

#include "core/Application.h"
#include "core/Logger.h"

using namespace PlaytimeEngine;

/**
 * @brief Sample application class
 */
class SampleApplication : public Application {
public:
    bool initialize() override {
        LOG_INFO("Initializing Sample Application");
        return true;
    }
    
    void update(float deltaTime) override {
        // Sample update logic
        (void)deltaTime;
    }
    
    void render() override {
        // Sample rendering
    }
    
    void shutdown() override {
        LOG_INFO("Shutting down Sample Application");
    }
    
    const char* getName() const override {
        return "Sample Application";
    }
    
    EngineConfig getEngineConfig() const override {
        EngineConfig config = EngineConfig::getDefault();
        config.renderConfig.windowTitle = "Project Playtime Sample";
        return config;
    }
};

IMPLEMENT_APPLICATION(SampleApplication)
