# UI Subsystem - Placeholder

add_library(EngineUI STATIC
    placeholder.cpp
)

target_include_directories(EngineUI
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EngineUI
    PUBLIC
        EngineCore
)

file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for UI subsystem\n"
    "namespace PlaytimeEngine { namespace UI { void placeholder() {} } }\n"
)
