# Plugin System - Placeholder

add_library(EnginePlugins STATIC
    placeholder.cpp
)

target_include_directories(EnginePlugins
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EnginePlugins
    PUBLIC
        EngineCore
)

file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for plugin system\n"
    "namespace PlaytimeEngine { namespace Plugins { void placeholder() {} } }\n"
)
