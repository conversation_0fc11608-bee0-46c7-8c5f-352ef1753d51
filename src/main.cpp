/**
 * @file main.cpp
 * @brief Main entry point for Project Playtime Engine
 * 
 * This file contains the main function that initializes and runs the engine.
 * It can be used for both the engine runtime and sample applications.
 */

#include "core/Engine.h"
#include "core/EngineConfig.h"
#include "core/Platform.h"
#include <iostream>
#include <exception>

using namespace PlaytimeEngine;

/**
 * @brief Print usage information
 */
void printUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [options]\n";
    std::cout << "Options:\n";
    std::cout << "  --help, -h          Show this help message\n";
    std::cout << "  --version, -v       Show version information\n";
    std::cout << "  --config <file>     Load configuration from file\n";
    std::cout << "  --windowed          Run in windowed mode\n";
    std::cout << "  --fullscreen        Run in fullscreen mode\n";
    std::cout << "  --width <width>     Set window width\n";
    std::cout << "  --height <height>   Set window height\n";
    std::cout << "  --vsync             Enable vertical sync\n";
    std::cout << "  --no-vsync          Disable vertical sync\n";
    std::cout << "  --debug             Enable debug mode\n";
    std::cout << "  --log-level <level> Set log level (0-5)\n";
}

/**
 * @brief Print version information
 */
void printVersion() {
    std::cout << "Project Playtime Engine v" << ENGINE_VERSION_MAJOR << "." 
              << ENGINE_VERSION_MINOR << "." << ENGINE_VERSION_PATCH << "\n";
    std::cout << "Built on " << __DATE__ << " at " << __TIME__ << "\n";
    
#if PLATFORM_WINDOWS
    std::cout << "Platform: Windows\n";
#elif PLATFORM_LINUX
    std::cout << "Platform: Linux\n";
#endif

#if BUILD_DEBUG
    std::cout << "Configuration: Debug\n";
#else
    std::cout << "Configuration: Release\n";
#endif

#if COMPILER_MSVC
    std::cout << "Compiler: MSVC\n";
#elif COMPILER_GCC
    std::cout << "Compiler: GCC\n";
#elif COMPILER_CLANG
    std::cout << "Compiler: Clang\n";
#endif
}

/**
 * @brief Parse command line arguments and configure the engine
 */
EngineConfig parseCommandLine(int argc, char* argv[]) {
    EngineConfig config;
    
    for (int i = 1; i < argc; ++i) {
        String arg = argv[i];
        
        if (arg == "--help" || arg == "-h") {
            printUsage(argv[0]);
            std::exit(0);
        }
        else if (arg == "--version" || arg == "-v") {
            printVersion();
            std::exit(0);
        }
        else if (arg == "--config" && i + 1 < argc) {
            config.configFile = argv[++i];
        }
        else if (arg == "--windowed") {
            config.renderConfig.fullscreen = false;
        }
        else if (arg == "--fullscreen") {
            config.renderConfig.fullscreen = true;
        }
        else if (arg == "--width" && i + 1 < argc) {
            config.renderConfig.windowWidth = std::atoi(argv[++i]);
        }
        else if (arg == "--height" && i + 1 < argc) {
            config.renderConfig.windowHeight = std::atoi(argv[++i]);
        }
        else if (arg == "--vsync") {
            config.renderConfig.vsync = true;
        }
        else if (arg == "--no-vsync") {
            config.renderConfig.vsync = false;
        }
        else if (arg == "--debug") {
            config.debugMode = true;
        }
        else if (arg == "--log-level" && i + 1 < argc) {
            config.logLevel = std::atoi(argv[++i]);
        }
        else {
            std::cerr << "Unknown argument: " << arg << "\n";
            printUsage(argv[0]);
            std::exit(1);
        }
    }
    
    return config;
}

/**
 * @brief Handle unhandled exceptions
 */
void handleException(const std::exception& e) {
    std::cerr << "Unhandled exception: " << e.what() << "\n";
    
    // Show platform-specific error dialog
    Platform::showMessageBox("Engine Error", 
                             String("An unhandled exception occurred:\n") + e.what(), 
                             true);
}

/**
 * @brief Main entry point
 */
int main(int argc, char* argv[]) {
    try {
        // Parse command line arguments
        EngineConfig config = parseCommandLine(argc, argv);
        
        // Initialize platform layer
        auto platformResult = Platform::initialize();
        if (platformResult != Platform::InitResult::Success) {
            std::cerr << "Failed to initialize platform layer\n";
            return -1;
        }
        
        // Create and initialize the engine
        auto engine = std::make_unique<Engine>();
        
        std::cout << "Initializing Project Playtime Engine...\n";
        auto initResult = engine->initialize(config);
        
        if (initResult != Engine::InitResult::Success) {
            std::cerr << "Failed to initialize engine: " << static_cast<int>(initResult) << "\n";
            Platform::shutdown();
            return -1;
        }
        
        std::cout << "Engine initialized successfully\n";
        std::cout << "Starting main loop...\n";
        
        // Run the main engine loop
        int exitCode = engine->run();
        
        std::cout << "Engine shutting down...\n";
        
        // Shutdown the engine
        engine->shutdown();
        engine.reset();
        
        // Shutdown platform layer
        Platform::shutdown();
        
        std::cout << "Engine shutdown complete\n";
        return exitCode;
    }
    catch (const std::exception& e) {
        handleException(e);
        return -1;
    }
    catch (...) {
        std::cerr << "Unknown exception occurred\n";
        Platform::showMessageBox("Engine Error", "An unknown exception occurred", true);
        return -1;
    }
}

#if PLATFORM_WINDOWS
/**
 * @brief Windows-specific entry point for GUI applications
 */
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    (void)hInstance;
    (void)hPrevInstance;
    (void)nCmdShow;
    
    // Parse command line from lpCmdLine
    // For simplicity, we'll just call main with empty arguments
    // In a real implementation, you'd parse lpCmdLine properly
    char* argv[] = { "PlaytimeEngine.exe" };
    return main(1, argv);
}
#endif
