# Project Playtime Engine - Source Directory CMake Configuration

# Core Engine Library
add_subdirectory(core)

# Rendering Subsystem
add_subdirectory(rendering)

# Physics Subsystem
add_subdirectory(physics)

# Audio Subsystem
add_subdirectory(audio)

# Animation Subsystem
add_subdirectory(animation)

# Scene Management Subsystem
add_subdirectory(scene)

# UI Subsystem
add_subdirectory(ui)

# Editor Subsystem
add_subdirectory(editor)

# Plugin System
add_subdirectory(plugins)

# Main Engine Target
add_library(ProjectPlaytimeEngine STATIC
    # Main engine entry point will be added here
)

# Link all subsystems to main engine
target_link_libraries(ProjectPlaytimeEngine
    PUBLIC
        EngineCore
        EngineRendering
        EnginePhysics
        EngineAudio
        EngineAnimation
        EngineScene
        EngineUI
        EnginePlugins
)

# Platform-specific linking
if(PLATFORM_WINDOWS)
    target_link_libraries(ProjectPlaytimeEngine
        PRIVATE
            d3d12
            dxgi
            dxguid
            d3dcompiler
    )
elseif(PLATFORM_LINUX)
    target_link_libraries(ProjectPlaytimeEngine
        PRIVATE
            ${Vulkan_LIBRARIES}
            pthread
            dl
    )
endif()

# External dependencies
target_link_libraries(ProjectPlaytimeEngine
    PUBLIC
        SDL2::SDL2
        SDL2::SDL2main
        EnTT::EnTT
)

# Include directories
target_include_directories(ProjectPlaytimeEngine
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Compiler definitions
target_compile_definitions(ProjectPlaytimeEngine
    PUBLIC
        ENGINE_EXPORTS
)

# Set target properties
set_target_properties(ProjectPlaytimeEngine PROPERTIES
    OUTPUT_NAME "PlaytimeEngine"
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Editor executable (optional)
option(BUILD_EDITOR "Build the engine editor" ON)
if(BUILD_EDITOR)
    add_executable(PlaytimeEditor
        editor/main.cpp
    )
    
    target_link_libraries(PlaytimeEditor
        PRIVATE
            ProjectPlaytimeEngine
    )
    
    set_target_properties(PlaytimeEditor PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
endif()

# Sample application
option(BUILD_SAMPLES "Build sample applications" ON)
if(BUILD_SAMPLES)
    add_executable(SampleApp
        samples/main.cpp
    )
    
    target_link_libraries(SampleApp
        PRIVATE
            ProjectPlaytimeEngine
    )
    
    set_target_properties(SampleApp PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
endif()
