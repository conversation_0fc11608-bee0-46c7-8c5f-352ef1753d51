# Rendering Subsystem - Placeholder

# This is a placeholder CMakeLists.txt for the rendering subsystem
# Will be implemented in future development phases

add_library(EngineRendering STATIC
    # Placeholder source file
    placeholder.cpp
)

target_include_directories(EngineRendering
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(EngineRendering
    PUBLIC
        EngineCore
)

# Create placeholder source file
file(WRITE ${CMAKE_CURRENT_SOURCE_DIR}/placeholder.cpp
    "// Placeholder for rendering subsystem\n"
    "namespace PlaytimeEngine { namespace Rendering { void placeholder() {} } }\n"
)
