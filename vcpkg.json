{"name": "project-playtime-engine", "version": "1.0.0", "description": "Next-generation high-performance game engine supporting 2D, 2.5D, and 3D development", "homepage": "https://github.com/Rolaand-Jayz/project-playtime-engine", "license": "MIT", "dependencies": [{"name": "sdl2", "version>=": "2.28.0", "features": ["vulkan"]}, {"name": "entt", "version>=": "3.12.0"}, {"name": "glm", "version>=": "0.9.9"}, {"name": "spdlog", "version>=": "1.12.0"}, {"name": "catch2", "version>=": "3.4.0"}, {"name": "imgui", "version>=": "1.89.0", "features": ["sdl2-binding", "opengl3-binding"]}], "features": {"vulkan": {"description": "Enable Vulkan support", "dependencies": [{"name": "vulkan", "platform": "linux"}, {"name": "vulkan-memory-allocator", "platform": "linux"}]}, "directx": {"description": "Enable DirectX 12 support", "dependencies": [{"name": "directx-headers", "platform": "windows"}, {"name": "directxmath", "platform": "windows"}]}, "physics": {"description": "Enable physics simulation", "dependencies": [{"name": "joltphysics", "version>=": "4.0.0"}]}, "audio": {"description": "Enable audio support", "dependencies": [{"name": "openal-soft", "version>=": "1.23.0"}]}, "testing": {"description": "Enable testing framework", "dependencies": [{"name": "gtest", "version>=": "1.14.0"}, {"name": "benchmark", "version>=": "1.8.0"}]}}, "overrides": [{"name": "sdl2", "version": "2.28.5"}]}