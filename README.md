# Project Playtime Engine

A next-generation high-performance game engine supporting 2D, 2.5D, and 3D game development with equal proficiency. Built for cross-platform compatibility (Windows and Linux) with a focus on real-time photorealism and stylized visuals.

## Features

### Multi-Dimensional Support
- **2D Games**: Native sprite rendering, 2D physics, and optimized 2D workflows
- **2.5D Games**: Seamless blending of 2D sprites with 3D environments
- **3D Games**: Cutting-edge 3D rendering with ray tracing and advanced lighting

### Core Subsystems
- **Rendering**: Unified pipeline supporting 2D sprites and 3D meshes with modern GPU features
- **Physics**: Both 2D (Box2D-style) and 3D physics simulation
- **Animation**: Sprite-based animation and skeletal animation systems
- **Audio**: 3D spatial audio with cross-platform support
- **Scene Management**: ECS-based architecture with efficient spatial partitioning
- **Multithreading**: Job system for maximum CPU utilization
- **Memory Management**: Custom allocators optimized for game workloads
- **Plugin Architecture**: Extensible design for custom features

### Cross-Platform
- **Windows**: DirectX 12, DLSS, XAudio2
- **Linux**: Vulkan, FSR, OpenAL
- Feature parity across platforms

## Documentation

See [`Project Playtime Engine.md`](./Project%20Playtime%20Engine.md) for comprehensive technical documentation, research, and implementation strategy.

## 🏗️ Current Implementation Status

### ✅ Completed Foundation
- **Project Structure**: Complete directory layout following modular design
- **Build System**: CMake configuration with cross-platform support and vcpkg integration
- **Core Math Library**: Vector2/3/4, Matrix3x3/4x4, Quaternion classes with SIMD optimization
- **Platform Abstraction**: Cross-platform interface for Windows/Linux differences
- **Type System**: Modern C++ type definitions and utilities
- **Test Framework**: Catch2-based testing infrastructure with comprehensive test coverage

### 🚧 In Progress
- **Engine Core**: Main engine class and subsystem coordination
- **Memory Management**: Custom allocators and tracking systems
- **Job System**: Work-stealing deque implementation
- **ECS Framework**: Entity Component System foundation

### 📋 Planned Next Steps
1. Complete core subsystem implementations
2. Renderer abstraction layer (DirectX 12/Vulkan)
3. Plugin system architecture
4. Physics integration (Jolt Physics)
5. Audio engine (miniaudio)
6. Animation system
7. Editor framework

## Getting Started

### Prerequisites
- **CMake 3.20+**
- **C++20 compatible compiler**:
  - Windows: Visual Studio 2022 or newer
  - Linux: GCC 11+ or Clang 13+
- **Platform SDKs**:
  - Windows: DirectX 12 Agility SDK
  - Linux: Vulkan SDK
- **vcpkg** (recommended for dependency management)

### Building the Engine

```bash
# Clone the repository
git clone https://github.com/Rolaand-Jayz/Project-Playtime.git
cd Project-Playtime

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Debug

# Build the engine
cmake --build . --config Debug

# Run tests
ctest --output-on-failure
```

### Dependencies

The engine uses the following external libraries:
- **SDL2**: Cross-platform windowing and input
- **EnTT**: Entity Component System framework
- **Catch2**: Testing framework
- **spdlog**: Logging library
- **ImGui**: Immediate mode GUI

## 📁 Project Structure

```
Project-Playtime/
├── src/                    # Engine source code
│   ├── core/              # Core utilities and math
│   ├── rendering/         # Graphics and rendering
│   ├── physics/           # Physics simulation
│   ├── audio/             # Audio engine
│   ├── animation/         # Animation systems
│   ├── scene/             # Scene management and ECS
│   ├── ui/                # User interface
│   ├── editor/            # Editor tools
│   └── plugins/           # Plugin system
├── include/               # Public header files
├── shaders/               # HLSL shader source files
├── assets/                # Sample assets and test content
├── tools/                 # Build scripts and utilities
├── docs/                  # Technical documentation
├── tests/                 # Unit and integration tests
├── CMakeLists.txt         # Root CMake configuration
├── vcpkg.json            # Dependency manifest
├── guide.md              # Comprehensive technical guide
└── shrimp-rules.md       # AI development standards
```

## 🧪 Testing

Run the test suite to verify engine functionality:

```bash
# Run all tests
cmake --build . --target run_tests

# Run specific test categories
cmake --build . --target run_core_tests
cmake --build . --target run_integration_tests
cmake --build . --target run_performance_tests
```

## Architecture Highlights

- **ECS (Entity Component System)**: Using EnTT for high-performance data-oriented design
- **Modern Graphics APIs**: DirectX 12 and Vulkan for maximum GPU utilization
- **Job System**: Multi-threaded task scheduling for CPU parallelism
- **Custom Memory Management**: Specialized allocators for different usage patterns
- **Plugin System**: Dynamic loading of game-specific or third-party modules

## Supported Technologies

### Graphics
- Ray Tracing (DXR, Vulkan RT)
- DLSS, FSR, XeSS upscaling
- PBR (Physically Based Rendering)
- Temporal Anti-Aliasing (TAA)
- Screen-Space Reflections (SSR)

### Physics
- 3D: PhysX, Jolt, or Bullet Physics
- 2D: Box2D integration
- Continuous Collision Detection (CCD)
- Deterministic simulation

### Audio
- 3D spatial audio
- Real-time effects processing
- Cross-platform audio backends

## 📚 Documentation

- **[Technical Guide](guide.md)**: Comprehensive engine architecture and implementation details
- **[Development Standards](shrimp-rules.md)**: AI agent development guidelines
- **[API Documentation](docs/)**: Generated API documentation (Doxygen)

## 🤝 Contributing

We welcome contributions! Please ensure you:

1. Follow the coding standards defined in `shrimp-rules.md`
2. Write tests for new functionality
3. Update documentation as needed
4. Ensure cross-platform compatibility

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🎯 Target Audience

Game developers targeting Windows and Linux platforms who need a high-performance engine capable of handling everything from pure 2D indie games to cutting-edge 3D AAA titles with advanced features like ray tracing and AI upscaling.

---

**Status**: 🚧 **Active Development** - Foundation phase complete, core systems in progress
