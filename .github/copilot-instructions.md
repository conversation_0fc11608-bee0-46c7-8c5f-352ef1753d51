# GitHub Copilot Instructions for Project Playtime Engine

## Project Context

You are working on **Project Playtime Engine**, a next-generation high-performance game engine that supports 2D, 2.5D, and 3D game development with equal proficiency. The engine enables real-time photorealism and stylized visuals across all dimensional paradigms, targeting game developers who need cross-platform compatibility between Windows (DirectX 12) and Linux (Vulkan) with true feature parity.

## Core Technology Stack

- **Language**: Modern C++ (C++20 or newer)
- **Build System**: CMake 3.20+ with vcpkg dependency management
- **Graphics APIs**: DirectX 12 Ultimate (Windows), Vulkan (Linux)
- **Physics**: Jolt Physics (primary), PhysX (optional GPU acceleration)
- **Audio**: miniaudio
- **ECS Framework**: EnTT
- **UI**: ImGui for editor, custom UI system for runtime
- **Testing**: Catch2
- **Logging**: spdlog

## Architecture Principles

### 1. Cross-Platform Abstraction
- Implement platform-specific backends behind unified interfaces
- Use conditional compilation: `#ifdef _WIN32` vs `#else` for Linux
- Abstract graphics APIs: `DX12Renderer` and `VulkanRenderer` implementing `IRenderer`
- Use HLSL shaders compiled to both DXIL (DX12) and SPIR-V (Vulkan) via DXC
- Never expose platform-specific APIs directly to engine core

### 2. Modular Subsystem Design
- Organize into distinct subsystems: Core, Rendering, Physics, Audio, Animation, UI, Scene, Memory, Threading
- Each subsystem as separate modules with clear interfaces
- Use dependency injection pattern - avoid singletons and global state
- Support plugin loading via DLLs (Windows) and .so files (Linux)

### 3. Data-Oriented Design
- Optimize memory access patterns and cache utilization
- Use EnTT ECS for component-based architecture
- Implement contiguous data layouts where possible
- Minimize pointer chasing and improve CPU cache efficiency

### 4. Performance Requirements
- Implement multithreaded command buffer recording for rendering
- Use job system for CPU parallelism across all subsystems
- Implement frame pipelining (2-3 frame buffer)
- Use GPU-driven culling and bindless resources

## Code Generation Guidelines

### Naming Conventions
- **Classes**: PascalCase (e.g., `RenderDevice`, `PhysicsWorld`)
- **Functions**: camelCase (e.g., `createBuffer`, `updateTransform`)
- **Variables**: camelCase (e.g., `vertexBuffer`, `deltaTime`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_LIGHTS`, `DEFAULT_GRAVITY`)
- **Files**: PascalCase for headers (e.g., `RenderDevice.h`), lowercase for source (e.g., `render_device.cpp`)

### Modern C++ Best Practices
- Use C++20 features: concepts, ranges, coroutines, modules (when compiler support allows)
- Follow C++ Core Guidelines
- Prefer RAII and smart pointers over raw pointers
- Use constexpr and const correctness
- Implement move semantics for heavy objects
- Use std::span for array-like parameters
- Prefer enum class over plain enums

### Memory Management
- Use custom allocators for performance-critical systems
- Implement object pools for frequently allocated/deallocated objects
- Use stack allocators for temporary allocations
- Minimize heap allocations in hot paths
- Implement memory tracking and leak detection in debug builds

### Threading and Concurrency
- Use the engine's job system for parallelism
- Avoid raw threads - use job system for task scheduling
- Implement lock-free data structures where appropriate
- Use atomic operations for simple shared state
- Design systems to minimize contention

## Subsystem-Specific Guidelines

### Rendering System
- Implement deferred shading pipeline with PBR materials
- Support both 2D sprite batching and 3D mesh rendering in unified pipeline
- Integrate ray tracing for reflections, shadows, and global illumination
- Implement upscaling support (DLSS, FSR, XeSS) via NVIDIA Streamline
- Use render graph system for pass management and resource dependencies

### Physics Integration
- Use Jolt Physics as primary backend
- Support both 2D and 3D physics simulation
- Run physics on fixed timestep (60Hz or 120Hz) with accumulation
- Implement deterministic mode for networking compatibility
- Sync physics transforms with ECS components via job system

### Animation System
- Support both skeletal animation (3D) and sprite animation (2D)
- Implement GPU skinning for skeletal meshes
- Support animation blending, state machines, and IK solvers
- Integrate with physics for ragdoll transitions

### Audio System
- Implement 3D spatial audio with HRTF
- Support multiple audio formats and streaming
- Use job system for audio processing
- Implement audio occlusion and reverb

## File Organization

When creating new files, follow this structure:
```
/src/
  /core/           - Math, memory, containers, OS abstraction
  /rendering/      - Graphics APIs, shaders, materials
  /physics/        - Physics integration
  /audio/          - Audio engine
  /animation/      - Skeletal and sprite animation
  /scene/          - ECS, scene graph, spatial partitioning
  /ui/             - ImGui integration and UI systems
  /editor/         - Editor-specific code
  /plugins/        - Plugin system and sample plugins
/include/          - Public headers organized by subsystem
/shaders/          - HLSL shader source files
/tests/            - Unit and integration tests
```

## Multi-File Coordination

When modifying files, consider these coordination requirements:

### README.md modifications:
- Update `guide.md` if architectural changes are described
- Verify consistency with project documentation

### Adding new subsystems:
- Update `CMakeLists.txt` with new source files
- Add corresponding interface headers to `/include/`
- Update plugin system registration if subsystem is pluggable
- Add unit tests in `/tests/` directory

### Shader modifications:
- Update both HLSL source and compilation scripts
- Verify cross-platform compatibility (DX12 and Vulkan)
- Update material system if new shader parameters added

### CMake changes:
- Use modern target-based approach
- Support both Debug and Release configurations
- Use FetchContent for external dependencies
- Generate separate targets for engine core, editor, and plugins

## Testing Requirements

### Test-Driven Development
- Write tests before implementing functionality
- Create unit tests for all modules
- Implement integration tests for subsystem interactions
- Add performance benchmarks for critical paths
- Aim for high test coverage (>80%)

### Test Organization
- Unit tests in `/tests/core/`, `/tests/rendering/`, etc.
- Integration tests in `/tests/integration/`
- Performance tests in `/tests/performance/`
- Use Catch2 testing framework
- Mock external dependencies for isolated testing

## Documentation Standards

### Code Documentation
- Use Doxygen comments for public APIs
- Document class purposes and usage patterns
- Explain complex algorithms and performance considerations
- Provide usage examples for public interfaces

### API Documentation
- Document all public functions with parameters and return values
- Explain preconditions and postconditions
- Document thread safety guarantees
- Provide performance characteristics (Big O notation where applicable)

## Plugin System

When implementing plugins:
- Define clear plugin interfaces
- Use version compatibility checking
- Implement plugin discovery and loading
- Support hot-reloading for development
- Provide plugin template and documentation

## Platform-Specific Considerations

### Windows (DirectX 12)
- Use DirectX 12 Agility SDK
- Implement GPU memory management via D3D12MA
- Use PIX for profiling and debugging
- Support Windows 10/11 specific features

### Linux (Vulkan)
- Use Vulkan SDK and validation layers
- Implement VMA for memory management
- Support major distributions (Ubuntu, Arch, etc.)
- Use RenderDoc for profiling and debugging

## Performance Optimization

### GPU Performance
- Use GPU-driven rendering techniques
- Implement efficient culling (frustum, occlusion, GPU-based)
- Batch draw calls and minimize state changes
- Use bindless textures and resources
- Implement GPU profiling and metrics

### CPU Performance
- Profile with tools like Perf (Linux) or Intel VTune
- Optimize hot paths identified by profiling
- Use SIMD intrinsics for math operations
- Implement efficient data structures (cache-friendly)
- Minimize allocations in frame loops

## Error Handling

- Use exceptions only for truly exceptional cases
- Prefer expected<T, Error> or optional<T> for recoverable errors
- Implement comprehensive logging with different severity levels
- Provide meaningful error messages with context
- Implement graceful degradation for missing features

## Security Considerations

- Validate all external inputs (assets, config files)
- Use safe string handling functions
- Implement buffer overflow protection
- Sanitize file paths and prevent directory traversal
- Use secure random number generation where needed

## Debugging and Profiling

- Implement debug visualization for all major systems
- Provide performance counters and metrics
- Support hot-reloading of shaders and assets
- Implement memory leak detection
- Provide CPU and GPU profiling integration

## Prohibited Practices

**Never:**
- Implement platform-specific code outside designated abstraction layers
- Use global variables or singletons (except for carefully designed system managers)
- Directly link against platform-specific libraries in core engine code
- Add dependencies that don't support both target platforms
- Use blocking I/O operations on main thread
- Implement graphics features that can't work on both DirectX 12 and Vulkan

**Always:**
- Reference `guide.md` for architectural decisions
- Implement cross-platform abstractions for OS-specific functionality
- Use the job system for CPU-intensive operations
- Profile performance impact of new features
- Write unit tests for new subsystems
- Update documentation when adding new APIs
- Verify shader compilation for both graphics backends
- Use RAII and modern C++ best practices

## Code Review Checklist

Before suggesting code:
1. ✅ Follows naming conventions
2. ✅ Uses modern C++ features appropriately
3. ✅ Implements proper error handling
4. ✅ Includes necessary documentation
5. ✅ Considers cross-platform compatibility
6. ✅ Follows data-oriented design principles
7. ✅ Integrates properly with existing subsystems
8. ✅ Includes appropriate tests
9. ✅ Considers performance implications
10. ✅ Follows project architecture guidelines

## Quick Reference

**Key Files:**
- `guide.md` - Technical architecture reference
- `shrimp-rules.md` - AI development standards
- `CMakeLists.txt` - Build configuration
- `vcpkg.json` - Dependency specification

**Primary Contacts:**
- Lead Developer: Rolaand Jayz

Remember: This engine targets AAA+ quality standards with professional game development workflows. Every suggestion should reflect this level of quality and attention to detail.
