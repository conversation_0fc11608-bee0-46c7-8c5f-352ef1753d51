# Project Playtime Engine - Test Suite

# Find or fetch testing framework
find_package(Catch2 3 QUIET)
if(NOT Catch2_FOUND)
    FetchContent_Declare(
        Catch2
        GIT_REPOSITORY https://github.com/catchorg/Catch2.git
        GIT_TAG v3.4.0
    )
    FetchContent_MakeAvailable(Catch2)
endif()

# Test configuration
enable_testing()

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/src)
include_directories(${CMAKE_SOURCE_DIR}/include)

# Core tests
add_subdirectory(core)

# Integration tests
add_subdirectory(integration)

# Performance tests
add_subdirectory(performance)

# Test utilities library
add_library(TestUtils STATIC
    utils/TestUtils.cpp
    utils/TestUtils.h
)

target_link_libraries(TestUtils
    PUBLIC
        Catch2::Catch2WithMain
        ProjectPlaytimeEngine
)

target_include_directories(TestUtils
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/utils
)

# Test runner executable
add_executable(TestRunner
    TestRunner.cpp
)

target_link_libraries(TestRunner
    PRIVATE
        TestUtils
        Catch2::Catch2WithMain
)

# Register tests with CTest
include(CTest)
include(Catch)

# Discover and register all Catch2 tests
catch_discover_tests(TestRunner)

# Custom test targets
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
    DEPENDS TestRunner
    COMMENT "Running all tests"
)

add_custom_target(run_core_tests
    COMMAND TestRunner "[core]"
    DEPENDS TestRunner
    COMMENT "Running core tests"
)

add_custom_target(run_integration_tests
    COMMAND TestRunner "[integration]"
    DEPENDS TestRunner
    COMMENT "Running integration tests"
)

add_custom_target(run_performance_tests
    COMMAND TestRunner "[performance]"
    DEPENDS TestRunner
    COMMENT "Running performance tests"
)

# Test coverage (if available)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    find_program(GCOV_PATH gcov)
    find_program(LCOV_PATH lcov)
    find_program(GENHTML_PATH genhtml)
    
    if(GCOV_PATH AND LCOV_PATH AND GENHTML_PATH)
        add_custom_target(coverage
            COMMAND ${LCOV_PATH} --directory . --capture --output-file coverage.info
            COMMAND ${LCOV_PATH} --remove coverage.info '/usr/*' --output-file coverage.info
            COMMAND ${LCOV_PATH} --list coverage.info
            COMMAND ${GENHTML_PATH} -o coverage coverage.info
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            COMMENT "Generating test coverage report"
        )
        
        add_dependencies(coverage TestRunner)
    endif()
endif()

# Memory leak detection (Windows)
if(PLATFORM_WINDOWS AND BUILD_DEBUG)
    target_compile_definitions(TestRunner
        PRIVATE
            _CRTDBG_MAP_ALLOC
    )
endif()

# Sanitizers (GCC/Clang)
if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND (COMPILER_GCC OR COMPILER_CLANG))
    option(ENABLE_ASAN "Enable AddressSanitizer" OFF)
    option(ENABLE_TSAN "Enable ThreadSanitizer" OFF)
    option(ENABLE_UBSAN "Enable UndefinedBehaviorSanitizer" OFF)
    
    if(ENABLE_ASAN)
        target_compile_options(TestRunner PRIVATE -fsanitize=address)
        target_link_options(TestRunner PRIVATE -fsanitize=address)
    endif()
    
    if(ENABLE_TSAN)
        target_compile_options(TestRunner PRIVATE -fsanitize=thread)
        target_link_options(TestRunner PRIVATE -fsanitize=thread)
    endif()
    
    if(ENABLE_UBSAN)
        target_compile_options(TestRunner PRIVATE -fsanitize=undefined)
        target_link_options(TestRunner PRIVATE -fsanitize=undefined)
    endif()
endif()
