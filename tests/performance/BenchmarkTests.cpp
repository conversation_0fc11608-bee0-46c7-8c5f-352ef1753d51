/**
 * @file BenchmarkTests.cpp
 * @brief Performance benchmark tests
 */

#include <catch2/catch_all.hpp>
#include "core/Math.h"
#include "TestUtils.h"

using namespace PlaytimeEngine;
using namespace PlaytimeEngine::Math;

TEST_CASE("Vector performance", "[performance][math]") {
    const uint32 iterations = 100000;
    
    BENCHMARK("Vector3 addition") {
        Vector3 v1(1.0f, 2.0f, 3.0f);
        Vector3 v2(4.0f, 5.0f, 6.0f);
        Vector3 result = v1 + v2;
        return result.x + result.y + result.z;
    };
    
    BENCHMARK("Vector3 dot product") {
        Vector3 v1(1.0f, 2.0f, 3.0f);
        Vector3 v2(4.0f, 5.0f, 6.0f);
        return v1.dot(v2);
    };
}
