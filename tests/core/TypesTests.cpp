/**
 * @file TypesTests.cpp
 * @brief Type system tests
 */

#include <catch2/catch_all.hpp>
#include "core/Types.h"
#include "TestUtils.h"

using namespace PlaytimeEngine;

TEST_CASE("Fixed-width types", "[core][types]") {
    REQUIRE(sizeof(int8) == 1);
    REQUIRE(sizeof(int16) == 2);
    REQUIRE(sizeof(int32) == 4);
    REQUIRE(sizeof(int64) == 8);
    
    REQUIRE(sizeof(uint8) == 1);
    REQUIRE(sizeof(uint16) == 2);
    REQUIRE(sizeof(uint32) == 4);
    REQUIRE(sizeof(uint64) == 8);
}

TEST_CASE("Alignment utilities", "[core][types]") {
    REQUIRE(alignUp(15u, 16u) == 16u);
    REQUIRE(alignUp(16u, 16u) == 16u);
    REQUIRE(alignUp(17u, 16u) == 32u);
    
    REQUIRE(alignDown(15u, 16u) == 0u);
    REQUIRE(alignDown(16u, 16u) == 16u);
    REQUIRE(alignDown(31u, 16u) == 16u);
    
    REQUIRE(isAligned(16u, 16u) == true);
    REQUIRE(isAligned(15u, 16u) == false);
    REQUIRE(isAligned(32u, 16u) == true);
}

TEST_CASE("Result type", "[core][types]") {
    Result<int, String> success(42);
    REQUIRE(success.hasValue());
    REQUIRE(!success.hasError());
    REQUIRE(success.value() == 42);
    
    Result<int, String> error("Test error");
    REQUIRE(!error.hasValue());
    REQUIRE(error.hasError());
    REQUIRE(error.error() == "Test error");
}

TEST_CASE("Optional type", "[core][types]") {
    Optional<int> empty;
    REQUIRE(!empty.hasValue());
    REQUIRE(!empty);
    
    Optional<int> value(42);
    REQUIRE(value.hasValue());
    REQUIRE(value);
    REQUIRE(value.value() == 42);
    REQUIRE(value.valueOr(0) == 42);
    
    REQUIRE(empty.valueOr(99) == 99);
}
