/**
 * @file MathTests.cpp
 * @brief Math library tests
 */

#include <catch2/catch_all.hpp>
#include "core/Math.h"
#include "TestUtils.h"

using namespace PlaytimeEngine;
using namespace PlaytimeEngine::Math;

TEST_CASE("Vector2 operations", "[core][math][vector2]") {
    SECTION("Construction") {
        Vector2 v1;
        REQUIRE(v1.x == 0.0f);
        REQUIRE(v1.y == 0.0f);
        
        Vector2 v2(1.0f, 2.0f);
        REQUIRE(v2.x == 1.0f);
        REQUIRE(v2.y == 2.0f);
    }
    
    SECTION("Arithmetic") {
        Vector2 v1(1.0f, 2.0f);
        Vector2 v2(3.0f, 4.0f);
        
        Vector2 sum = v1 + v2;
        REQUIRE(sum.x == 4.0f);
        REQUIRE(sum.y == 6.0f);
        
        Vector2 diff = v2 - v1;
        REQUIRE(diff.x == 2.0f);
        REQUIRE(diff.y == 2.0f);
    }
    
    SECTION("Dot product") {
        Vector2 v1(1.0f, 2.0f);
        Vector2 v2(3.0f, 4.0f);
        
        float dot = v1.dot(v2);
        REQUIRE(dot == 11.0f); // 1*3 + 2*4 = 11
    }
}

TEST_CASE("Vector3 operations", "[core][math][vector3]") {
    SECTION("Construction") {
        Vector3 v1;
        REQUIRE(v1.x == 0.0f);
        REQUIRE(v1.y == 0.0f);
        REQUIRE(v1.z == 0.0f);
        
        Vector3 v2(1.0f, 2.0f, 3.0f);
        REQUIRE(v2.x == 1.0f);
        REQUIRE(v2.y == 2.0f);
        REQUIRE(v2.z == 3.0f);
    }
    
    SECTION("Cross product") {
        Vector3 v1(1.0f, 0.0f, 0.0f);
        Vector3 v2(0.0f, 1.0f, 0.0f);
        
        Vector3 cross = v1.cross(v2);
        REQUIRE(cross.x == 0.0f);
        REQUIRE(cross.y == 0.0f);
        REQUIRE(cross.z == 1.0f);
    }
}

TEST_CASE("Math constants", "[core][math][constants]") {
    REQUIRE(PI > 3.14f);
    REQUIRE(PI < 3.15f);
    REQUIRE(DEG_TO_RAD > 0.017f);
    REQUIRE(RAD_TO_DEG > 57.0f);
}
