/**
 * @file PlatformTests.cpp
 * @brief Platform abstraction tests
 */

#include <catch2/catch_all.hpp>
#include "core/Platform.h"
#include "TestUtils.h"

using namespace PlaytimeEngine;

TEST_CASE("Platform information", "[core][platform]") {
    auto info = Platform::getPlatformInfo();
    
    REQUIRE(!info.platformName.empty());
    REQUIRE(info.cpuCoreCount > 0);
    REQUIRE(info.totalMemory > 0);
}

TEST_CASE("Directory operations", "[core][platform]") {
    String currentDir = Platform::getCurrentDirectory();
    REQUIRE(!currentDir.empty());
    
    String execDir = Platform::getExecutableDirectory();
    REQUIRE(!execDir.empty());
}

TEST_CASE("Path operations", "[core][platform]") {
    String path1 = "test/path";
    String path2 = "file.txt";
    String combined = Platform::combinePaths(path1, path2);
    REQUIRE(!combined.empty());
    
    String normalized = Platform::normalizePath("test\\path/file.txt");
    REQUIRE(!normalized.empty());
}
