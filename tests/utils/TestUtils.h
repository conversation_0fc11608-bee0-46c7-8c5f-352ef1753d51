#pragma once

/**
 * @file TestUtils.h
 * @brief Test utilities and helpers
 */

#include <catch2/catch_all.hpp>
#include "core/Types.h"

namespace PlaytimeEngine {
namespace TestUtils {

/**
 * @brief Test fixture base class
 */
class TestFixture {
public:
    virtual ~TestFixture() = default;
    virtual void setUp() {}
    virtual void tearDown() {}
};

/**
 * @brief Math comparison helpers
 */
namespace Math {
    /**
     * @brief Check if two floats are approximately equal
     */
    bool approxEqual(float a, float b, float epsilon = 1e-6f);
    
    /**
     * @brief Check if two doubles are approximately equal
     */
    bool approxEqual(double a, double b, double epsilon = 1e-9);
}

/**
 * @brief Memory testing helpers
 */
namespace Memory {
    /**
     * @brief Check for memory leaks
     */
    bool checkForLeaks();
    
    /**
     * @brief Get current memory usage
     */
    size_t getCurrentUsage();
}

/**
 * @brief Performance testing helpers
 */
namespace Performance {
    /**
     * @brief Benchmark a function
     */
    template<typename Func>
    double benchmark(Func&& func, uint32 iterations = 1000) {
        auto start = std::chrono::high_resolution_clock::now();
        
        for (uint32 i = 0; i < iterations; ++i) {
            func();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        return static_cast<double>(duration.count()) / iterations;
    }
}

} // namespace TestUtils
} // namespace PlaytimeEngine
