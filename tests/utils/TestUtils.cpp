/**
 * @file TestUtils.cpp
 * @brief Test utilities implementation
 */

#include "TestUtils.h"
#include <cmath>

namespace PlaytimeEngine {
namespace TestUtils {
namespace Math {

bool approxEqual(float a, float b, float epsilon) {
    return std::abs(a - b) <= epsilon;
}

bool approxEqual(double a, double b, double epsilon) {
    return std::abs(a - b) <= epsilon;
}

} // namespace Math

namespace Memory {

bool checkForLeaks() {
    // Memory leak checking will be implemented when memory tracking is complete
    return true;
}

size_t getCurrentUsage() {
    // Memory usage tracking will be implemented when memory tracking is complete
    return 0;
}

} // namespace Memory

} // namespace TestUtils
} // namespace PlaytimeEngine
