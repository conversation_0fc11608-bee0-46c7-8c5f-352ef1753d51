/**
 * @file TestRunner.cpp
 * @brief Main test runner for Project Playtime Engine
 * 
 * This file contains the main entry point for running all engine tests
 * using the Catch2 testing framework.
 */

#include <catch2/catch_all.hpp>
#include "core/Engine.h"
#include "core/Math.h"
#include "core/Platform.h"

using namespace PlaytimeEngine;

/**
 * @brief Test listener for engine-specific setup and teardown
 */
class EngineTestListener : public Catch::EventListenerBase {
public:
    using EventListenerBase::EventListenerBase;

    void testRunStarting(Catch::TestRunInfo const&) override {
        // Initialize platform layer for tests
        Platform::initialize();
        Math::initialize();
    }

    void testRunEnded(Catch::TestRunStats const&) override {
        // Cleanup after all tests
        Math::shutdown();
        Platform::shutdown();
    }

    void testCaseStarting(Catch::TestCaseInfo const& testInfo) override {
        // Per-test setup if needed
        (void)testInfo; // Suppress unused parameter warning
    }

    void testCaseEnded(Catch::TestCaseStats const& testCaseStats) override {
        // Per-test cleanup if needed
        (void)testCaseStats; // Suppress unused parameter warning
    }
};

CATCH_REGISTER_LISTENER(EngineTestListener)

/**
 * @brief Basic engine initialization test
 */
TEST_CASE("Engine can be created and destroyed", "[core][engine]") {
    SECTION("Engine creation") {
        auto engine = std::make_unique<Engine>();
        REQUIRE(engine != nullptr);
        REQUIRE(engine->getState() == Engine::State::Uninitialized);
    }
}

/**
 * @brief Platform layer tests
 */
TEST_CASE("Platform layer functionality", "[core][platform]") {
    SECTION("Platform info") {
        auto info = Platform::getPlatformInfo();
        REQUIRE(!info.platformName.empty());
        REQUIRE(info.cpuCoreCount > 0);
        REQUIRE(info.totalMemory > 0);
    }
    
    SECTION("Directory operations") {
        String currentDir = Platform::getCurrentDirectory();
        REQUIRE(!currentDir.empty());
        
        String execDir = Platform::getExecutableDirectory();
        REQUIRE(!execDir.empty());
    }
    
    SECTION("Path operations") {
        String path1 = "test/path";
        String path2 = "file.txt";
        String combined = Platform::combinePaths(path1, path2);
        REQUIRE(!combined.empty());
        
        String normalized = Platform::normalizePath("test\\path/file.txt");
        REQUIRE(!normalized.empty());
    }
    
    SECTION("Memory operations") {
        void* ptr = Platform::alignedAlloc(1024, 64);
        REQUIRE(ptr != nullptr);
        REQUIRE(Platform::isAligned(reinterpret_cast<uintptr_t>(ptr), 64));
        Platform::alignedFree(ptr);
    }
}

/**
 * @brief Math library tests
 */
TEST_CASE("Math library functionality", "[core][math]") {
    SECTION("Constants") {
        REQUIRE(Math::PI > 3.14f);
        REQUIRE(Math::PI < 3.15f);
        REQUIRE(Math::DEG_TO_RAD > 0.017f);
        REQUIRE(Math::RAD_TO_DEG > 57.0f);
    }
    
    SECTION("Vector2 operations") {
        Math::Vector2 v1(1.0f, 2.0f);
        Math::Vector2 v2(3.0f, 4.0f);
        
        Math::Vector2 sum = v1 + v2;
        REQUIRE(sum.x == 4.0f);
        REQUIRE(sum.y == 6.0f);
        
        float dot = v1.dot(v2);
        REQUIRE(dot == 11.0f); // 1*3 + 2*4 = 11
        
        float length = v1.length();
        REQUIRE(length == Catch::Approx(2.236f).epsilon(0.001f));
    }
    
    SECTION("Vector3 operations") {
        Math::Vector3 v1(1.0f, 0.0f, 0.0f);
        Math::Vector3 v2(0.0f, 1.0f, 0.0f);
        
        Math::Vector3 cross = v1.cross(v2);
        REQUIRE(cross.x == 0.0f);
        REQUIRE(cross.y == 0.0f);
        REQUIRE(cross.z == 1.0f);
        
        float dot = v1.dot(v2);
        REQUIRE(dot == 0.0f);
    }
    
    SECTION("Vector4 operations") {
        Math::Vector4 v1(1.0f, 2.0f, 3.0f, 4.0f);
        Math::Vector4 v2(5.0f, 6.0f, 7.0f, 8.0f);
        
        Math::Vector4 sum = v1 + v2;
        REQUIRE(sum.x == 6.0f);
        REQUIRE(sum.y == 8.0f);
        REQUIRE(sum.z == 10.0f);
        REQUIRE(sum.w == 12.0f);
        
        float dot = v1.dot(v2);
        REQUIRE(dot == 70.0f); // 1*5 + 2*6 + 3*7 + 4*8 = 70
    }
    
    SECTION("Quaternion operations") {
        Math::Quaternion q1 = Math::Quaternion::identity();
        REQUIRE(q1.x == 0.0f);
        REQUIRE(q1.y == 0.0f);
        REQUIRE(q1.z == 0.0f);
        REQUIRE(q1.w == 1.0f);
        
        Math::Quaternion q2 = Math::Quaternion::rotationY(Math::PI * 0.5f);
        Math::Vector3 forward(0.0f, 0.0f, 1.0f);
        Math::Vector3 rotated = q2.rotate(forward);
        
        REQUIRE(rotated.x == Catch::Approx(1.0f).epsilon(0.001f));
        REQUIRE(rotated.y == Catch::Approx(0.0f).epsilon(0.001f));
        REQUIRE(rotated.z == Catch::Approx(0.0f).epsilon(0.001f));
    }
    
    SECTION("Matrix operations") {
        Math::Matrix4x4 identity = Math::Matrix4x4::identity();
        REQUIRE(identity(0, 0) == 1.0f);
        REQUIRE(identity(1, 1) == 1.0f);
        REQUIRE(identity(2, 2) == 1.0f);
        REQUIRE(identity(3, 3) == 1.0f);
        REQUIRE(identity(0, 1) == 0.0f);
        
        Math::Matrix4x4 translation = Math::Matrix4x4::translation(1.0f, 2.0f, 3.0f);
        Math::Vector3 point(0.0f, 0.0f, 0.0f);
        Math::Vector3 transformed = translation.transformPoint(point);
        
        REQUIRE(transformed.x == 1.0f);
        REQUIRE(transformed.y == 2.0f);
        REQUIRE(transformed.z == 3.0f);
    }
}

/**
 * @brief Type system tests
 */
TEST_CASE("Type system functionality", "[core][types]") {
    SECTION("Fixed-width types") {
        REQUIRE(sizeof(int8) == 1);
        REQUIRE(sizeof(int16) == 2);
        REQUIRE(sizeof(int32) == 4);
        REQUIRE(sizeof(int64) == 8);
        
        REQUIRE(sizeof(uint8) == 1);
        REQUIRE(sizeof(uint16) == 2);
        REQUIRE(sizeof(uint32) == 4);
        REQUIRE(sizeof(uint64) == 8);
    }
    
    SECTION("Alignment utilities") {
        REQUIRE(alignUp(15u, 16u) == 16u);
        REQUIRE(alignUp(16u, 16u) == 16u);
        REQUIRE(alignUp(17u, 16u) == 32u);
        
        REQUIRE(alignDown(15u, 16u) == 0u);
        REQUIRE(alignDown(16u, 16u) == 16u);
        REQUIRE(alignDown(31u, 16u) == 16u);
        
        REQUIRE(isAligned(16u, 16u) == true);
        REQUIRE(isAligned(15u, 16u) == false);
        REQUIRE(isAligned(32u, 16u) == true);
    }
    
    SECTION("Result type") {
        Result<int, String> success(42);
        REQUIRE(success.hasValue());
        REQUIRE(!success.hasError());
        REQUIRE(success.value() == 42);
        
        Result<int, String> error("Test error");
        REQUIRE(!error.hasValue());
        REQUIRE(error.hasError());
        REQUIRE(error.error() == "Test error");
    }
    
    SECTION("Optional type") {
        Optional<int> empty;
        REQUIRE(!empty.hasValue());
        REQUIRE(!empty);
        
        Optional<int> value(42);
        REQUIRE(value.hasValue());
        REQUIRE(value);
        REQUIRE(value.value() == 42);
        REQUIRE(value.valueOr(0) == 42);
        
        REQUIRE(empty.valueOr(99) == 99);
    }
}
