/**
 * @file EngineTests.cpp
 * @brief Engine integration tests
 */

#include <catch2/catch_all.hpp>
#include "core/Engine.h"
#include "TestUtils.h"

using namespace PlaytimeEngine;

TEST_CASE("Engine creation", "[integration][engine]") {
    auto engine = std::make_unique<Engine>();
    REQUIRE(engine != nullptr);
    REQUIRE(engine->getState() == Engine::State::Uninitialized);
}

TEST_CASE("Engine configuration", "[integration][engine]") {
    EngineConfig config = EngineConfig::getDefault();
    REQUIRE(config.renderConfig.windowWidth > 0);
    REQUIRE(config.renderConfig.windowHeight > 0);
}
